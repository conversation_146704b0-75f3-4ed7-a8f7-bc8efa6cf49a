package com.fastbee.services.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fastbee.common.annotation.Log;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.enums.BusinessType;
import com.fastbee.services.domain.ServiceCommonFault;
import com.fastbee.services.service.IServiceCommonFaultService;
import com.fastbee.common.utils.poi.ExcelUtil;
import com.fastbee.common.core.page.TableDataInfo;

/**
 * 常见故障Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@RestController
@RequestMapping("/services/fault")
public class ServiceCommonFaultController extends BaseController
{
    @Autowired
    private IServiceCommonFaultService serviceCommonFaultService;

    /**
     * 查询常见故障列表
     */

    @GetMapping("/list")
    public TableDataInfo list(ServiceCommonFault serviceCommonFault)
    {
        startPage();
        List<ServiceCommonFault> list = serviceCommonFaultService.selectServiceCommonFaultList(serviceCommonFault);
        return getDataTable(list);
    }

    /**
     * 导出常见故障列表
     */
    @PreAuthorize("@ss.hasPermi('services:fault:export')")
    @Log(title = "常见故障", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceCommonFault serviceCommonFault)
    {
        List<ServiceCommonFault> list = serviceCommonFaultService.selectServiceCommonFaultList(serviceCommonFault);
        ExcelUtil<ServiceCommonFault> util = new ExcelUtil<ServiceCommonFault>(ServiceCommonFault.class);
        util.exportExcel(response, list, "常见故障数据");
    }

    /**
     * 获取常见故障详细信息
     */

    @GetMapping(value = "/{faultId}")
    public AjaxResult getInfo(@PathVariable("faultId") Long faultId)
    {
        return success(serviceCommonFaultService.selectServiceCommonFaultByFaultId(faultId));
    }

    /**
     * 新增常见故障
     */
    @PreAuthorize("@ss.hasPermi('services:fault:add')")
    @Log(title = "常见故障", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceCommonFault serviceCommonFault)
    {
        return toAjax(serviceCommonFaultService.insertServiceCommonFault(serviceCommonFault));
    }

    /**
     * 修改常见故障
     */
    @PreAuthorize("@ss.hasPermi('services:fault:edit')")
    @Log(title = "常见故障", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceCommonFault serviceCommonFault)
    {
        return toAjax(serviceCommonFaultService.updateServiceCommonFault(serviceCommonFault));
    }

    /**
     * 删除常见故障
     */
    @PreAuthorize("@ss.hasPermi('services:fault:remove')")
    @Log(title = "常见故障", businessType = BusinessType.DELETE)
	@DeleteMapping("/{faultIds}")
    public AjaxResult remove(@PathVariable Long[] faultIds)
    {
        return toAjax(serviceCommonFaultService.deleteServiceCommonFaultByFaultIds(faultIds));
    }
}
