/**
 * WebSocket 配置文件
 * 统一管理所有WebSocket连接相关的配置
 * 支持开发环境和生产环境的不同配置
 */

// 环境配置 - 手动控制开关
// 🔧 修改这里来切换开发/生产环境
const IS_DEVELOPMENT = false; // true: 开发环境, false: 生产环境

console.log('🌍 当前环境:', IS_DEVELOPMENT ? '开发环境' : '生产环境');
console.log('🌍 WebSocket配置:', {
  host: IS_DEVELOPMENT ? 'localhost' : 'ljht.mydmc.cn',
  port: IS_DEVELOPMENT ? 8080 : 8443,
  sockjs: IS_DEVELOPMENT ? 'ws://localhost:8080/ws' : 'wss://ljht.mydmc.cn:8443/ws'
});
 
// WebSocket 配置
export const WEBSOCKET_CONFIG = {
  // 基础配置
  host: IS_DEVELOPMENT ? 'localhost' : 'ljht.mydmc.cn',
  port: IS_DEVELOPMENT ? 8080 : 8443, // 生产环境使用nginx的8443端口
  
  // 不同端点配置
  endpoints: {
    // SockJS端点（STOMP协议）
    sockjs: '/ws',
    // 原生WebSocket端点
    native: '/ws-native',
    // 微信小程序专用端点
    mp: '/ws-mp',
    // 微信小程序测试端点（已测试成功）
    test: '/ws-test',
    // 管理员专用端点
    admin: '/ws'
  },
  
  // 连接配置
  connection: {
    // 重连配置
    maxReconnectAttempts: 5,
    reconnectInterval: 3000, // 3秒
    // 连接超时
    timeout: 15000, // 15秒
    // 心跳配置
    heartbeatInterval: 30000, // 30秒
    pingTimeout: 10000 // 10秒
  },
  
  // 协议配置
  protocols: [],
  
  // 请求头配置
  headers: {
    userIdHeader: 'userId',
    tokenHeader: 'token',
    userTypeHeader: 'userType'
  },
  
  // 调试配置
  debug: {
    enabled: IS_DEVELOPMENT,
    logLevel: IS_DEVELOPMENT ? 'debug' : 'error'
  }
}

// API 配置
export const API_CONFIG = {
  // 后端API地址
  baseURL: IS_DEVELOPMENT ? 'http://localhost:8080' : 'https://ljht.mydmc.cn:8443/prod-api',
  timeout: 10000
}
 
// MQTT 配置
export const MQTT_CONFIG = {
  // MQTT WebSocket地址
  wsUrl: IS_DEVELOPMENT ? 'ws://emqx.mydmc.cn:18083/mqtt' : 'wss://emqx.mydmc.cn:18084/mqtt',
  // MQTT over WebSocket (H5环境)
  wssUrl: 'wss://emqx.mydmc.cn:18084/mqtt'
}

/**
 * 获取完整的WebSocket URL
 * @param {string} endpoint 端点类型: 'sockjs' | 'native' | 'admin'
 * @param {boolean} secure 是否使用安全连接
 * @returns {string} 完整的WebSocket URL
 */ 
export function getWebSocketUrl(endpoint = 'native', secure = false) {
  const { host, port, endpoints } = WEBSOCKET_CONFIG
  const path = endpoints[endpoint] || endpoints.native
  
  // 根据环境自动选择协议
  let protocol
  if (IS_DEVELOPMENT) {
    protocol = secure ? 'wss:' : 'ws:'
  } else {
    // 生产环境使用HTTPS端口，默认使用安全连接
    protocol = 'wss:'
  }
  
  return `${protocol}//${host}:${port}${path}`
}

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export function getApiBaseUrl() {
  return API_CONFIG.baseURL
}

/**
 * 获取MQTT URL (根据环境自动选择)
 * @returns {string} MQTT WebSocket URL
 */
export function getMqttUrl() {
  // #ifdef H5
  return MQTT_CONFIG.wssUrl
  // #endif
  
  // #ifdef MP-WEIXIN || APP-PLUS
  return MQTT_CONFIG.wsUrl
  // #endif
  
  // 默认使用WebSocket
  return MQTT_CONFIG.wsUrl
}
 
// 导出默认配置
export default {
  WEBSOCKET_CONFIG,
  API_CONFIG,
  MQTT_CONFIG,
  getWebSocketUrl,
  getApiBaseUrl,
  getMqttUrl
}

