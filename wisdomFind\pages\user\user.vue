<template>
  <view class="container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image
          class="avatar"
          :src="
            userInfo.avatar ||
            'https://img.icons8.com/ios-filled/50/000000/cat-profile.png'
          "
          @click.stop="previewAvatar"
        />
        <view class="user-detail">
          <text class="nickname">{{ userInfo.name || "未设置昵称" }}</text>
          <text class="user-type">普通用户</text>
        </view>
      </view>

      <view class="level-box">
        <text>我的等级</text>
        <view class="level-progress">
          <view class="progress-bar">
            <view class="progress" :style="{ width: '67%' }"></view>
          </view>
          <text class="level-text">Lv7</text>
          <text class="exp-text">1358/2000</text>
        </view>
      </view>

      <view class="user-actions">
        <view class="action-item">
          <uni-icons type="heart" size="28" color="#bbb" />
          <text>我的动态</text>
        </view>
        <view class="action-item">
          <uni-icons type="videocam" size="28" color="#bbb" />
          <text>我的视频</text>
        </view>
        <view class="action-item" @click="gotoComments">
          <uni-icons type="chatbubble" size="28" color="#bbb" />
          <text>我的评论</text>
          <text v-if="unreadCommentCount > 0" class="badge">{{
            unreadCommentCount
          }}</text>
        </view>
      </view>
    </view>

    <!-- 商城订单 -->
    <view class="order-section">
      <view class="order-header">
        <text>商城订单</text>
        <text class="all-orders" @click="gotoOrder(0)">全部订单</text>
      </view>
      <view class="order-status-list">
        <view class="order-status-item" @click="gotoOrder(1)">
          <uni-icons type="wallet" size="28" color="#bbb" />
          <text>待付款</text>
        </view>
        <view class="order-status-item" @click="gotoOrder(2)">
          <uni-icons type="paperplane" size="28" color="#bbb" />
          <text>待发货</text>
        </view>
        <view class="order-status-item" @click="gotoOrder(3)">
          <uni-icons type="shop" size="28" color="#bbb" />
          <text>待收货</text>
        </view>
        <view class="order-status-item" @click="gotoOrder(4)">
          <uni-icons type="star" size="28" color="#bbb" />
          <text>待评价</text>
        </view>
        <view class="order-status-item" @click="gotoOrder(5)">
          <uni-icons type="undo" size="28" color="#bbb" />
          <text>退款售后</text>
        </view>
      </view>
    </view>

    <!-- 功能区 -->
    <view class="feature-section">
      <view class="feature-list">
        <view class="feature-item">
          <uni-icons type="person" size="30" color="#333" @click="gotoPerson" />
          <text>账户设置</text>
        </view>
        <view class="feature-item">
          <uni-icons type="cart" size="30" color="#333" />
          <text>购物车</text>
        </view>
        <view class="feature-item">
          <uni-icons type="calendar" size="30" color="#333" />
          <text>每日签到</text>
        </view>
        <view class="feature-item">
          <uni-icons type="gift" size="30" color="#333" />
          <text>积分商城</text>
        </view>
        <view class="feature-item">
          <uni-icons type="email" size="30" color="#333" />
          <text>增值信函</text>
        </view>
        <view class="feature-item">
          <uni-icons type="medal" size="30" color="#333" />
          <text>会员中心</text>
        </view>
        <view class="feature-item">
          <uni-icons type="settings" size="30" color="#333" />
          <text>设备管理</text>
        </view>
        <view class="feature-item">
          <uni-icons type="eye" size="30" color="#333" />
          <text>虚拟体验</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onUnmounted } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { getUserInfo } from "@/api/user.js";
import { getUnreadCount } from "@/api/notification.js";

const id = ref();
const userInfo = ref({
  name: "",
  avatar: "",
  phone: "",
  sex: "",
  address: "",
});

const unreadCommentCount = ref(0);

// 获取用户信息的方法
const fetchUserData = async () => {
  try {
    const userData = uni.getStorageSync("userData");
    if (userData && userData.userId) {
      id.value = userData.userId;

      // 先从本地缓存快速显示数据
      if (userData.name || userData.avatar) {
        userInfo.value = {
          ...userInfo.value,
          name: userData.name || userInfo.value.name,
          avatar: userData.avatar || userInfo.value.avatar,
        };
      }

      // 再从服务器获取最新信息
      const response = await getUserInfo(userData.userId);
      if (response.code === 200 || response.code === 0) {
        userInfo.value = response.data;

        // 如果服务器数据与本地不同，更新本地缓存
        if (
          userData.name !== response.data.name ||
          userData.avatar !== response.data.avatar
        ) {
          const updatedUserData = {
            ...userData,
            name: response.data.name,
            avatar: response.data.avatar,
          };
          uni.setStorageSync("userData", updatedUserData);
        }
      } else {
        console.error("获取用户信息失败:", response.msg);
        // 只有在没有缓存数据时才显示错误
        if (!userInfo.value.name) {
          uni.showToast({
            title: "获取用户信息失败",
            icon: "none",
          });
        }
      }
    } else {
      console.error("未找到用户数据");
      uni.showToast({
        title: "请先登录",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取用户信息异常:", error);
    // 只有在没有缓存数据时才显示错误
    if (!userInfo.value.name) {
      uni.showToast({
        title: "网络异常，请重试",
        icon: "none",
      });
    }
  }
};

// 监听用户信息更新事件
const handleUserInfoUpdate = (updatedUserInfo) => {
  console.log("我的账户页面收到用户信息更新通知:", updatedUserInfo);
  // 立即更新页面显示的用户信息
  userInfo.value = {
    ...userInfo.value,
    name: updatedUserInfo.name || userInfo.value.name,
    avatar: updatedUserInfo.avatar || userInfo.value.avatar,
  };
};

// 监听评论未读数量更新事件
const handleCommentUnreadCountUpdate = (count) => {
  console.log("我的账户页面收到评论未读数量更新通知:", count);
  unreadCommentCount.value = count;
};

// 从本地存储读取用户信息并获取完整用户数据
onLoad(async () => {
  await fetchUserData();
  await loadUnreadCommentCount();

  // 监听用户信息更新事件
  uni.$on("userInfoUpdated", handleUserInfoUpdate);
  // 监听评论未读数量更新事件
  uni.$on("updateCommentUnreadCount", handleCommentUnreadCountUpdate);
});

// 页面显示时重新加载未读评论数量
onShow(async () => {
  await loadUnreadCommentCount();
});

// 页面卸载时清理事件监听
onUnmounted(() => {
  uni.$off("userInfoUpdated", handleUserInfoUpdate);
  uni.$off("updateCommentUnreadCount", handleCommentUnreadCountUpdate);
});

// 跳转到订单页面，携带tabIndex参数
const gotoOrder = (tabIndex) => {
  uni.navigateTo({
    url: `/shop/orderAll/orderAll?userId=${id.value}&tabIndex=${tabIndex}`,
  });
};

const gotoPerson = () => {
  uni.navigateTo({
    url: `/person/personInfo/personInfo?userId=${id.value}`,
  });
};

// 加载未读评论数量
const loadUnreadCommentCount = async () => {
  try {
    if (id.value) {
      const res = await getUnreadCount(id.value);
      if (res && res.code === 200) {
        unreadCommentCount.value = res.data.count || 0;
      }
    }
  } catch (e) {
    console.error("获取未读评论数量失败:", e);
  }
};

// 跳转到评论页面
const gotoComments = () => {
  uni.navigateTo({
    url: `/person/my/comments`,
  });
};

// 预览头像图片
const previewAvatar = () => {
  const avatarUrl = userInfo.value.avatar;

  // 如果没有自定义头像，使用默认头像
  if (
    !avatarUrl ||
    avatarUrl === "https://img.icons8.com/ios-filled/50/000000/cat-profile.png"
  ) {
    uni.showToast({
      title: "暂无头像图片",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  // 使用uni.previewImage预览图片
  uni.previewImage({
    urls: [avatarUrl], // 图片地址数组
    current: avatarUrl, // 当前显示的图片
    indicator: "number", // 显示数字指示器
    loop: false, // 不循环
    success: () => {
      console.log("头像预览成功");
    },
    fail: (error) => {
      console.error("头像预览失败:", error);
      uni.showToast({
        title: "图片预览失败",
        icon: "none",
        duration: 2000,
      });
    },
  });
};
</script>

<style scoped>
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
  box-sizing: border-box;
}

.user-card {
  margin: 30rpx 20rpx;
  background: linear-gradient(135deg, #d0f5e8 0%, #e6f7ef 100%);
  border-radius: 20rpx;
  padding: 36rpx 24rpx 30rpx 24rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.08);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: #fff;
  margin-right: 24rpx;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2rpx solid #f0f0f0;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.avatar:active {
  transform: scale(0.98);
}

.user-detail {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 34rpx;
  font-weight: bold;
}

.user-type {
  font-size: 24rpx;
  color: #6abf8e;
  margin-top: 8rpx;
}

.level-box {
  margin-top: 30rpx;
  background: #fff;
  border-radius: 14rpx;
  padding: 22rpx 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.level-progress {
  display: flex;
  align-items: center;
  margin-top: 14rpx;
}

.progress-bar {
  flex: 1;
  height: 14rpx;
  background: #e0f2e9;
  border-radius: 7rpx;
  margin-right: 20rpx;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: #6abf8e;
  border-radius: 7rpx;
}

.level-text {
  margin-left: 12rpx;
  font-size: 26rpx;
  color: #6abf8e;
}

.exp-text {
  margin-left: 14rpx;
  font-size: 24rpx;
  color: #999;
}

.user-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 32rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #888;
  font-size: 24rpx;
  position: relative;
}

.action-item uni-icons {
  margin-bottom: 8rpx;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #f56c6c;
  color: #fff;
  border-radius: 20rpx;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

.order-section {
  background: #fff;
  margin: 30rpx 20rpx;
  border-radius: 18rpx;
  padding: 26rpx 0 10rpx 0;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.08);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 28rpx 16rpx 28rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.all-orders {
  font-size: 24rpx;
  color: #6abf8e;
}

.order-status-list {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0 30rpx 0;
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #888;
  font-size: 24rpx;
}

.order-status-item uni-icons {
  margin-bottom: 8rpx;
}

.feature-section {
  background: #fff;
  margin: 30rpx 20rpx;
  border-radius: 18rpx;
  padding: 26rpx 0 10rpx 0;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.08);
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.feature-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 36rpx;
  font-size: 24rpx;
  color: #333;
}

.feature-item uni-icons {
  margin-bottom: 10rpx;
}
</style>
