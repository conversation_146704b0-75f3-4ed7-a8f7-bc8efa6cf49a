package com.fastbee.services.service.Impl;

import java.util.List;
import com.fastbee.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fastbee.services.mapper.ServiceTelephoneCustomerMapper;
import com.fastbee.services.domain.ServiceTelephoneCustomer;
import com.fastbee.services.service.IServiceTelephoneCustomerService;

/**
 * 电话客服信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class ServiceTelephoneCustomerServiceImpl implements IServiceTelephoneCustomerService 
{
    @Autowired
    private ServiceTelephoneCustomerMapper serviceTelephoneCustomerMapper;

    /**
     * 查询电话客服信息
     * 
     * @param csId 电话客服信息主键
     * @return 电话客服信息
     */
    @Override
    public ServiceTelephoneCustomer selectServiceTelephoneCustomerByCsId(Long csId)
    {
        return serviceTelephoneCustomerMapper.selectServiceTelephoneCustomerByCsId(csId);
    }

    /**
     * 查询电话客服信息列表
     * 
     * @param serviceTelephoneCustomer 电话客服信息
     * @return 电话客服信息
     */
    @Override
    public List<ServiceTelephoneCustomer> selectServiceTelephoneCustomerList(ServiceTelephoneCustomer serviceTelephoneCustomer)
    {
        return serviceTelephoneCustomerMapper.selectServiceTelephoneCustomerList(serviceTelephoneCustomer);
    }

    /**
     * 新增电话客服信息
     * 
     * @param serviceTelephoneCustomer 电话客服信息
     * @return 结果
     */
    @Override
    public int insertServiceTelephoneCustomer(ServiceTelephoneCustomer serviceTelephoneCustomer)
    {
        serviceTelephoneCustomer.setCreateTime(DateUtils.getNowDate());
        return serviceTelephoneCustomerMapper.insertServiceTelephoneCustomer(serviceTelephoneCustomer);
    }

    /**
     * 修改电话客服信息
     * 
     * @param serviceTelephoneCustomer 电话客服信息
     * @return 结果
     */
    @Override
    public int updateServiceTelephoneCustomer(ServiceTelephoneCustomer serviceTelephoneCustomer)
    {
        serviceTelephoneCustomer.setUpdateTime(DateUtils.getNowDate());
        return serviceTelephoneCustomerMapper.updateServiceTelephoneCustomer(serviceTelephoneCustomer);
    }

    /**
     * 批量删除电话客服信息
     * 
     * @param csIds 需要删除的电话客服信息主键
     * @return 结果
     */
    @Override
    public int deleteServiceTelephoneCustomerByCsIds(Long[] csIds)
    {
        return serviceTelephoneCustomerMapper.deleteServiceTelephoneCustomerByCsIds(csIds);
    }
    /**
     * 删除电话客服信息信息
     * 
     * @param csId 电话客服信息主键
     * @return 结果
     */
    @Override
    public int deleteServiceTelephoneCustomerByCsId(Long csId)
    {
        return serviceTelephoneCustomerMapper.deleteServiceTelephoneCustomerByCsId(csId);
    }
}