{
  "name": "wisdomFind",
  "appid": "__UNI__A8212B3",
  "description": "",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  "app-plus": {
    /* 5+App特有相关 */
    "usingComponents": true,
    "nvueCompiler": "uni-app",
    "nvueStyleCompiler": "uni-app",
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "modules": {},
    /* 模块配置 */
    "distribute": {
      /* 应用发布信息 */
      "android": {
        /* android打包配置 */
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
        ]
      },
      "ios": {},
      /* ios打包配置 */
      "sdkConfigs": {}
    }
  },
  /* SDK配置 */
  "quickapp": {},
  /* 快应用特有相关 */
  "mp-weixin": {
    /* 小程序特有相关 */
    "appid": "wx9943030a75833166",
    "setting": {
      "urlCheck": false,
      "postcss": false,
      "minified": true,
      "es6": true
    },
    "usingComponents": true,
    "permission": {
      "scope.userLocation": {
        "desc": "我们需要获取您的位置信息，用于设备联网和服务推荐"
      },
      "scope.camera": {
        "desc": "我们需要使用您的摄像头拍摄视频和照片"
      },
      "scope.album": {
        "desc": "我们需要访问您的相册选择视频和图片"
      }
    },
    "lazyCodeLoading": "requiredComponents"
  },
  "vueVersion": "3"
}
