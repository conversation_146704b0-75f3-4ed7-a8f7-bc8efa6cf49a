<template>
  <view class="nested-comment-container">
    <!-- 评论标题 -->
    <view class="comment-section-header">
      <text class="comment-title">评论</text>
    </view>

    <!-- 顶级评论 -->
    <view
      v-for="comment in getTopLevelComments()"
      :key="comment.commentId"
      :id="`comment-${comment.commentId}`"
      :class="[
        'top-comment',
        {
          'highlight-comment':
            highlightedCommentId && comment.commentId == highlightedCommentId,
        },
      ]"
    >
      <!-- 评论者信息 -->
      <view class="comment-header">
        <image
          :src="comment.avatar || '/static/images/placeholder.png'"
          class="avatar"
        ></image>
        <view class="user-info">
          <view class="username">
            {{ comment.nickName }}
            <text v-if="comment.isAuthor" class="author-badge">作者</text>
          </view>
          <view class="comment-time">{{ formatTime(comment.createTime) }}</view>
        </view>
        <view class="comment-actions">
          <view class="like-btn" @click="likeComment(comment)">
            <uni-icons
              type="heart"
              :color="comment.isLiked ? '#ff4757' : '#999'"
              size="16"
            ></uni-icons>
            <text :class="{ liked: comment.isLiked }">{{
              comment.likeCount || 0
            }}</text>
          </view>
          <view class="reply-btn" @click="showReplyInput(comment)">
            <uni-icons type="chat" color="#999" size="16"></uni-icons>
            <text>回复</text>
          </view>
        </view>
      </view>

      <!-- 评论内容 -->
      <view class="comment-content">{{
        comment.commentContent || comment.content
      }}</view>

      <!-- 三层楼中楼回复列表 -->
      <view v-if="hasReplies(comment)" class="replies-container">
        <!-- 顶级评论和第一个回复之间的横线 -->
        <view class="top-reply-separator"></view>

        <!-- 加载中状态 -->
        <view v-if="isLoadingReplies(comment)" class="loading-replies">
          <uni-load-more
            status="loading"
            content-text="{ contentText: { contentdown: '加载回复中...', contentrefresh: '加载回复中...', contentnomore: '加载回复中...' } }"
          ></uni-load-more>
        </view>

        <!-- 渲染三层楼中楼结构 -->
        <view
          v-for="reply in getLevel2Replies(comment)"
          :key="reply.commentId"
          class="level2-reply-container"
        >
          <!-- 二层回复 (Level 2) -->
          <view
            :id="`comment-${reply.commentId}`"
            :class="[
              'reply-item level2-reply',
              {
                'highlight-comment':
                  highlightedCommentId &&
                  reply.commentId == highlightedCommentId,
              },
            ]"
          >
            <view class="reply-header">
              <image
                :src="reply.avatar || '/static/images/placeholder.png'"
                class="reply-avatar"
              ></image>
              <view class="reply-user-info">
                <text class="reply-username">{{ reply.nickName }}</text>
                <text v-if="reply.isAuthor" class="author-badge">作者</text>
                <text class="reply-target">回复 @{{ comment.nickName }}</text>
              </view>
              <view class="reply-actions">
                <view class="like-btn" @click="likeComment(reply)">
                  <uni-icons
                    type="heart"
                    :color="reply.isLiked ? '#ff4757' : '#999'"
                    size="14"
                  ></uni-icons>
                  <text :class="{ liked: reply.isLiked }">{{
                    reply.likeCount || 0
                  }}</text>
                </view>
                <view class="reply-btn" @click="showReplyInput(reply, comment)">
                  <uni-icons type="chat" color="#999" size="14"></uni-icons>
                </view>
              </view>
            </view>
            <view class="reply-content">{{
              reply.commentContent || reply.content
            }}</view>
            <view class="reply-time">{{ formatTime(reply.createTime) }}</view>
          </view>

          <!-- 三层回复 (Level 3) - 显示在二层回复下面，更多缩进 -->
          <view v-if="hasLevel3Replies(reply)" class="level3-replies-container">
            <view
              v-for="level3Reply in getDisplayLevel3Replies(reply)"
              :key="level3Reply.commentId"
              :id="`comment-${level3Reply.commentId}`"
              :class="[
                'reply-item level3-reply',
                {
                  'highlight-comment':
                    highlightedCommentId &&
                    level3Reply.commentId == highlightedCommentId,
                },
              ]"
            >
              <view class="reply-header">
                <image
                  :src="level3Reply.avatar || '/static/images/placeholder.png'"
                  class="reply-avatar"
                ></image>
                <view class="reply-user-info">
                  <text class="reply-username">{{ level3Reply.nickName }}</text>
                  <text v-if="level3Reply.isAuthor" class="author-badge"
                    >作者</text
                  >
                  <text class="reply-target"
                    >回复 @{{
                      level3Reply.replyToUserName || reply.nickName
                    }}</text
                  >
                </view>
                <view class="reply-actions">
                  <view class="like-btn" @click="likeComment(level3Reply)">
                    <uni-icons
                      type="heart"
                      :color="level3Reply.isLiked ? '#ff4757' : '#999'"
                      size="14"
                    ></uni-icons>
                    <text :class="{ liked: level3Reply.isLiked }">{{
                      level3Reply.likeCount || 0
                    }}</text>
                  </view>
                  <view
                    class="reply-btn"
                    @click="showReplyInput(level3Reply, comment, reply)"
                  >
                    <uni-icons type="chat" color="#999" size="14"></uni-icons>
                  </view>
                </view>
              </view>
              <view class="reply-content">{{
                level3Reply.commentContent || level3Reply.content
              }}</view>
              <view class="reply-time">{{
                formatTime(level3Reply.createTime)
              }}</view>
            </view>

            <!-- 二层回复的折叠/展开按钮 - 控制三层回复 -->
            <view
              v-if="getLevel3RepliesCount(reply) > 3"
              class="fold-toggle-btn level3-fold-btn"
              @click="toggleLevel3RepliesFold(reply)"
            >
              <view class="fold-content">
                <uni-icons
                  :type="getLevel3FoldState(reply) ? 'arrow-down' : 'arrow-up'"
                  color="#007aff"
                  size="14"
                ></uni-icons>
                <text class="fold-text">
                  {{
                    getLevel3FoldState(reply)
                      ? `查看全部${getLevel3RepliesCount(reply)}条回复`
                      : "收起回复"
                  }}
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 顶级评论的折叠/展开按钮 - 控制该顶级评论下的二层回复 -->
        <view
          v-if="getLevel2RepliesCount(comment) > 3"
          class="fold-toggle-btn"
          @click="toggleLevel2RepliesFold(comment)"
        >
          <view class="fold-content">
            <uni-icons
              :type="getLevel2FoldState(comment) ? 'arrow-down' : 'arrow-up'"
              color="#007aff"
              size="14"
            ></uni-icons>
            <text class="fold-text">
              {{
                getLevel2FoldState(comment)
                  ? `查看全部${getLevel2RepliesCount(comment)}条回复`
                  : "收起回复"
              }}
            </text>
          </view>
        </view>
      </view>

      <!-- 回复输入框 - 移回到评论内部 -->
      <view
        v-if="
          comment && showReplyFor === (comment.commentId || comment.replyId)
        "
        class="reply-input-container"
      >
        <input
          v-model="replyText"
          :placeholder="replyPlaceholder"
          class="reply-input"
          @confirm="submitReply"
        />
        <button class="submit-reply-btn" @click="submitReply">发送</button>
        <button class="cancel-reply-btn" @click="hideReplyInput">取消</button>
      </view>
    </view>

    <!-- 顶级评论折叠/展开按钮 -->
    <view
      v-if="
        getTopLevelComments().length <
        commentList.filter((c) => !c.parentId || c.parentId === 0).length
      "
      class="top-fold-toggle-btn"
      @click="toggleTopCommentsFold()"
    >
      <view class="top-fold-content">
        <uni-icons
          :type="getTopFoldState() ? 'arrow-down' : 'arrow-up'"
          color="#007aff"
          size="16"
        ></uni-icons>
        <text class="top-fold-text">
          {{
            getTopFoldState()
              ? `查看全部${
                  commentList.filter((c) => !c.parentId || c.parentId === 0)
                    .length
                }条评论`
              : "收起评论"
          }}
        </text>
      </view>
    </view>

    <!-- 无评论时的提示 -->
    <view v-if="commentList.length === 0" class="no-comments">
      <uni-icons type="chat" size="60" color="#ccc"></uni-icons>
      <text class="no-comments-text">暂无评论，来发表第一条评论吧</text>
    </view>

    <!-- 主评论输入框 -->
    <view
      id="commentInput"
      class="main-comment-input"
      :class="{ 'highlight-input': shouldHighlightInput }"
    >
      <view v-if="shouldHighlightInput" class="input-tip">
        👇 点击这里开始评论
      </view>
      <input
        v-model="mainCommentText"
        placeholder="写下你的评论..."
        class="comment-input"
        @confirm="submitMainComment"
        @focus="shouldHighlightInput = false"
      />
      <button
        class="submit-btn"
        :disabled="!mainCommentText.trim()"
        @click="submitMainComment"
      >
        发送
      </button>
    </view>
  </view>
</template>

<script>
import { formatTime } from "@/utils/timeUtils.js";

export default {
  name: "NestedComment",
  props: {
    imageTextId: {
      type: [String, Number],
      required: false,
    },
    postId: {
      type: [String, Number],
      required: false,
    },
    commentList: {
      type: Array,
      default: () => [],
    },
    scrollToCommentId: {
      type: [String, Number],
      required: false,
    },
  },

  mounted() {
    console.log("NestedComment组件已挂载");
    console.log("imageTextId:", this.imageTextId);
    console.log("postId:", this.postId);
    console.log("commentList:", this.commentList);
    console.log("scrollToCommentId:", this.scrollToCommentId);

    // 如果有智能跳转目标，设置高亮评论ID
    if (this.scrollToCommentId) {
      console.log("🎯 [智能跳转] 设置目标评论ID:", this.scrollToCommentId);
      this.highlightedCommentId = this.scrollToCommentId;
      this.smartJumpMode = true;

      // 延迟执行智能跳转预加载
      this.$nextTick(() => {
        setTimeout(() => {
          this.preloadForSmartJump();
        }, 500);
      });
    }

    // 检查输入框元素是否存在
    this.$nextTick(() => {
      setTimeout(() => {
        // 在组件内查询
        const query = uni.createSelectorQuery().in(this);
        query.select("#commentInput").boundingClientRect();
        query.exec((res) => {
          console.log("NestedComment mounted - 组件内查询结果:", res);
          if (res[0]) {
            console.log("✅ NestedComment 组件内找到输入框元素");
          } else {
            console.log("❌ NestedComment 组件内未找到输入框元素");
          }
        });

        // 全局查询
        const globalQuery = uni.createSelectorQuery();
        globalQuery.select("#commentInput").boundingClientRect();
        globalQuery.exec((globalRes) => {
          console.log("NestedComment mounted - 全局查询结果:", globalRes);
          if (globalRes[0]) {
            console.log("✅ NestedComment 全局找到输入框元素");
          } else {
            console.log("❌ NestedComment 全局未找到输入框元素");
          }
        });
      }, 500);
    });
  },

  watch: {
    commentList: {
      handler(newVal) {
        console.log("🎯 [NestedComment] commentList更新:", newVal.length);

        // 调试：分析数据结构
        if (newVal.length > 0) {
          const typeStats = {};
          newVal.forEach((comment) => {
            const type = comment.type || "undefined";
            typeStats[type] = (typeStats[type] || 0) + 1;
          });
          console.log("🎯 [NestedComment] 评论类型统计:", typeStats);

          // 显示前几条数据的结构
          console.log(
            "🎯 [NestedComment] 前3条数据结构:",
            newVal.slice(0, 3).map((c) => ({
              id: c.commentId,
              parentId: c.parentId,
              type: c.type,
              replyCount: c.replyCount,
              content: c.commentContent?.substring(0, 30) + "...",
            }))
          );
        }

        // 初始化折叠状态
        this.initializeFoldStates(newVal);
      },
      immediate: true,
    },
  },
  data() {
    return {
      mainCommentText: "",
      replyText: "",
      showReplyFor: null, // 显示回复框的评论ID
      replyTarget: null, // 回复目标（用户信息）
      replyPlaceholder: "写下你的回复...",
      shouldHighlightInput: false, // 控制输入框高亮提示
      foldedComments: new Set(), // 记录哪些评论的回复是折叠状态
      foldedSubReplies: new Set(), // 记录哪些回复的子回复是折叠状态
      loadedReplies: new Set(), // 记录已加载过子回复的评论ID
      loadingReplies: new Set(), // 记录正在加载子回复的评论ID
      topCommentsExpanded: false, // 记录顶级评论是否展开
      highlightedCommentId: null, // 需要高亮显示的评论ID（用于智能跳转）
      isExpandingForTarget: false, // 是否正在为智能跳转展开评论
      smartJumpMode: false, // 是否处于智能跳转模式
    };
  },
  methods: {
    formatTime,

    // 检查评论是否有回复（包括未加载的）
    hasReplies(comment) {
      return (
        (comment.replies && comment.replies.length > 0) ||
        (comment.replyCount && comment.replyCount > 0)
      );
    },

    // 检查是否正在加载回复
    isLoadingReplies(comment) {
      const commentId = comment.commentId || comment.replyId;
      return this.loadingReplies.has(commentId);
    },

    // 计算回复的缩进距离（参考nit-forum的扁平化设计）
    getReplyIndent(reply, parentComment) {
      // 如果回复的是顶级评论，不需要缩进
      if (
        !reply.replyToUserName ||
        reply.replyToUserName === parentComment.nickName
      ) {
        return 0;
      }

      // 参考nit-forum设计：所有回复都是平级的，最多只有一级缩进
      // 避免无限递归和过度嵌套，提供更好的视觉体验
      return 40; // 固定一级缩进，简化视觉层级
    },

    // 初始化折叠状态
    initializeFoldStates(comments) {
      if (!comments || !Array.isArray(comments)) return;

      // 如果正在为智能跳转展开评论，不要重置展开状态
      if (this.isExpandingForTarget) {
        console.log("🎯 [NestedComment] 正在智能跳转展开中，跳过状态重置");
        return;
      }

      // 清空旧状态
      this.foldedComments.clear();
      this.foldedSubReplies.clear();
      this.topCommentsExpanded = false; // 默认折叠顶级评论

      comments.forEach((comment) => {
        // 如果回复数量大于3条，默认折叠（不加入集合，因为默认就是折叠）
        // foldedComments集合用来记录已经展开的评论

        // 处理子回复的折叠状态
        if (comment.replies && comment.replies.length > 0) {
          comment.replies.forEach((reply) => {
            // 如果子回复数量大于2条，默认折叠
            if (reply.replies && reply.replies.length > 2) {
              // 默认折叠，不加入集合
            }
          });
        }
      });
    },

    // 获取顶级评论列表（三层楼中楼结构）
    getTopLevelComments() {
      // 过滤出顶级评论（parentId为null、0或undefined的评论）
      const topComments = this.commentList.filter(
        (comment) => !comment.parentId || comment.parentId === 0
      );

      // 如果顶级评论数量小于等于5条，全部显示
      if (topComments.length <= 5) {
        console.log(
          "🎯 [NestedComment] 顶级评论数量<=5，显示全部:",
          topComments.length
        );
        return topComments;
      }

      // 如果已展开，显示全部顶级评论
      if (this.topCommentsExpanded) {
        console.log(
          "🎯 [NestedComment] 已展开，显示全部顶级评论:",
          topComments.length
        );
        return topComments;
      }

      // 默认折叠状态，只显示最新的5条顶级评论
      console.log("🎯 [NestedComment] 折叠状态，只显示前5条顶级评论");
      return topComments.slice(0, 5);
    },

    // 获取二层回复（直接回复顶级评论的回复）
    getLevel2Replies(topComment) {
      const commentId = topComment.commentId;

      // 从扁平化数据中过滤出属于这个顶级评论的二层回复
      // 二层回复的特征：parentId === topComment.commentId 且 type === 2
      const allReplies = this.commentList.filter(
        (comment) =>
          comment.parentId === commentId &&
          (comment.type === 2 || comment.type === "2")
      );

      console.log(
        `🎯 [NestedComment] 顶级评论${commentId}的二层回复数据:`,
        allReplies.map((r) => ({
          id: r.commentId,
          parentId: r.parentId,
          type: r.type,
          content: r.commentContent?.substring(0, 20) + "...",
        }))
      );

      const isExpanded = this.foldedComments.has(commentId);

      // 如果已展开，显示全部二层回复
      if (isExpanded) {
        console.log(
          `🎯 [NestedComment] 顶级评论${commentId}已展开，显示全部${allReplies.length}条二层回复`
        );
        return allReplies;
      }

      // 如果总回复数量小于等于3条，全部显示
      if (allReplies.length <= 3) {
        console.log(
          `🎯 [NestedComment] 顶级评论${commentId}回复数量<=3，显示全部${allReplies.length}条`
        );
        return allReplies;
      }

      // 默认折叠状态，只显示前3条
      console.log(
        `🎯 [NestedComment] 顶级评论${commentId}折叠状态，只显示前3条二层回复`
      );
      return allReplies.slice(0, 3);
    },

    // 获取三层回复（回复二层回复的回复）
    getLevel3Replies(level2Reply) {
      const replyId = level2Reply.commentId;

      // 从扁平化数据中过滤出属于这个二层回复的三层回复
      // 三层回复的特征：parentId === level2Reply.commentId 且 type === 3
      const level3Replies = this.commentList.filter(
        (comment) =>
          comment.parentId === replyId &&
          (comment.type === 3 || comment.type === "3")
      );

      console.log(
        `🎯 [NestedComment] 二层回复${replyId}的三层回复数据:`,
        level3Replies.map((r) => ({
          id: r.commentId,
          parentId: r.parentId,
          type: r.type,
          content: r.commentContent?.substring(0, 20) + "...",
        }))
      );
      return level3Replies;
    },

    // 检查是否有三层回复
    hasLevel3Replies(level2Reply) {
      return this.getLevel3Replies(level2Reply).length > 0;
    },

    // 获取要显示的三层回复列表（根据折叠状态）
    getDisplayLevel3Replies(level2Reply) {
      const level3Replies = this.getLevel3Replies(level2Reply);
      const replyId = level2Reply.commentId || level2Reply.replyId;

      // 检查是否已展开（在foldedSubReplies集合中表示已展开）
      const isExpanded = this.foldedSubReplies.has(replyId);
      console.log(
        `🎯 [NestedComment] 二层回复${replyId}三层回复展开状态检查:`,
        isExpanded,
        "foldedSubReplies:",
        Array.from(this.foldedSubReplies)
      );

      // 检查是否包含目标评论（智能跳转场景）
      if (
        this.highlightedCommentId &&
        level3Replies.some(
          (reply) =>
            (reply.commentId || reply.replyId) == this.highlightedCommentId
        )
      ) {
        console.log(
          `🎯 [NestedComment] 二层回复${replyId}包含目标评论${this.highlightedCommentId}，三层回复数量:`,
          level3Replies.length
        );

        // 强制展开包含目标评论的回复
        if (!isExpanded) {
          console.log(
            `🎯 [NestedComment] 强制展开二层回复${replyId}以显示目标评论${this.highlightedCommentId}`
          );
          this.foldedSubReplies.add(replyId);
        }
        return level3Replies;
      }

      // 如果已展开，显示全部三层回复
      if (isExpanded) {
        console.log(
          `🎯 [NestedComment] 二层回复${replyId}三层回复已展开，显示全部${level3Replies.length}条`
        );
        return level3Replies;
      }

      // 如果三层回复数量小于等于3条，全部显示
      if (level3Replies.length <= 3) {
        console.log(
          `🎯 [NestedComment] 二层回复${replyId}三层回复数量<=3，显示全部:`,
          level3Replies.length
        );
        return level3Replies;
      }

      // 默认折叠状态，只显示前3条
      console.log(
        `🎯 [NestedComment] 二层回复${replyId}三层回复折叠状态，只显示前3条`
      );
      return level3Replies.slice(0, 3);
    },

    // 获取二层回复数量（优先使用后端返回的replyCount）
    getLevel2RepliesCount(topComment) {
      // 优先使用后端返回的replyCount字段，这是真实的总数量
      if (
        topComment.replyCount !== undefined &&
        topComment.replyCount !== null
      ) {
        return topComment.replyCount;
      }

      // 降级方案：计算当前已加载的回复数量
      return this.commentList.filter(
        (comment) => comment.parentId === topComment.commentId
      ).length;
    },

    // 获取三层回复数量（优先使用后端返回的replyCount）
    getLevel3RepliesCount(level2Reply) {
      // 优先使用后端返回的replyCount字段，这是真实的总数量
      if (
        level2Reply.replyCount !== undefined &&
        level2Reply.replyCount !== null
      ) {
        return level2Reply.replyCount;
      }

      // 降级方案：计算当前已加载的三层回复数量
      return this.getLevel3Replies(level2Reply).length;
    },

    // 获取二层回复折叠状态（true表示折叠，false表示展开）
    getLevel2FoldState(topComment) {
      const commentId = topComment.commentId;
      const level2Count = this.getLevel2RepliesCount(topComment);

      // 如果二层回复数量大于3条：默认折叠（不在集合中），展开时加入集合
      if (level2Count > 3) {
        const isExpanded = this.foldedComments.has(commentId);
        console.log(
          `🎯 [NestedComment] 顶级评论${commentId}二层回复折叠状态检查: 回复${level2Count}条, 展开状态:${isExpanded}, 后端replyCount:${topComment.replyCount}`
        );
        return !isExpanded; // 不在集合中=折叠状态
      }
      console.log(
        `🎯 [NestedComment] 顶级评论${commentId}回复数量<=3条(${level2Count})，不显示折叠按钮, 后端replyCount:${topComment.replyCount}`
      );
      return false; // 回复数量<=3条时不显示折叠按钮
    },

    // 获取三层回复折叠状态（true表示折叠，false表示展开）
    getLevel3FoldState(level2Reply) {
      const replyId = level2Reply.commentId || level2Reply.replyId;
      const level3Count = this.getLevel3RepliesCount(level2Reply);

      // 如果三层回复数量大于3条：默认折叠（不在集合中），展开时加入集合
      if (level3Count > 3) {
        const isExpanded = this.foldedSubReplies.has(replyId);
        console.log(
          `🎯 [NestedComment] 二层回复${replyId}三层回复折叠状态检查: 回复${level3Count}条, 展开状态:${isExpanded}, 后端replyCount:${level2Reply.replyCount}`
        );
        return !isExpanded; // 不在集合中=折叠状态
      }
      console.log(
        `🎯 [NestedComment] 二层回复${replyId}三层回复数量<=3条(${level3Count})，不显示折叠按钮, 后端replyCount:${level2Reply.replyCount}`
      );
      return false; // 回复数量<=3条时不显示折叠按钮
    },

    // 切换二层回复折叠状态
    toggleLevel2RepliesFold(topComment) {
      const commentId = topComment.commentId;
      const level2Count = this.getLevel2RepliesCount(topComment);

      console.log(
        `🎯 [NestedComment] 切换顶级评论${commentId}的二层回复折叠状态，回复数量:${level2Count}`
      );

      if (this.foldedComments.has(commentId)) {
        // 当前是展开状态，点击后折叠（从集合中移除）
        this.foldedComments.delete(commentId);
        console.log(`🎯 [NestedComment] 折叠顶级评论${commentId}的二层回复`);
      } else {
        // 当前是折叠状态，点击后展开（加入集合）
        this.foldedComments.add(commentId);
        console.log(`🎯 [NestedComment] 展开顶级评论${commentId}的二层回复`);

        // 检查是否需要加载更多数据
        const currentLevel2Count = this.commentList.filter(
          (c) => c.parentId === commentId
        ).length;

        console.log(
          `🎯 [NestedComment] 当前已加载二层回复: ${currentLevel2Count}, 总数: ${level2Count}`
        );

        // 如果当前加载的数据少于总数，说明需要加载更多
        if (level2Count > currentLevel2Count) {
          console.log(
            `🎯 [NestedComment] 需要加载更多二层回复，触发load-more-replies事件`
          );
          this.$emit("load-more-replies", commentId);
        } else {
          console.log(`🎯 [NestedComment] 所有二层回复已加载，直接展开显示`);
        }
      }

      // 强制更新视图
      this.$forceUpdate();
    },

    // 切换三层回复折叠状态
    toggleLevel3RepliesFold(level2Reply) {
      const replyId = level2Reply.commentId || level2Reply.replyId;
      const level3Count = this.getLevel3RepliesCount(level2Reply);

      console.log(
        `🎯 [NestedComment] 切换二层回复${replyId}的三层回复折叠状态，回复数量:${level3Count}`
      );

      if (this.foldedSubReplies.has(replyId)) {
        // 当前是展开状态，点击后折叠（从集合中移除）
        this.foldedSubReplies.delete(replyId);
        console.log(`🎯 [NestedComment] 折叠二层回复${replyId}的三层回复`);
      } else {
        // 当前是折叠状态，点击后展开（加入集合）
        this.foldedSubReplies.add(replyId);
        console.log(`🎯 [NestedComment] 展开二层回复${replyId}的三层回复`);

        // 检查是否需要加载更多三层回复数据
        const currentLevel3Count = this.getLevel3Replies(level2Reply).length;

        console.log(
          `🎯 [NestedComment] 当前已加载三层回复: ${currentLevel3Count}, 总数: ${level3Count}`
        );

        // 如果当前加载的数据少于总数，说明需要加载更多
        if (level3Count > currentLevel3Count) {
          console.log(
            `🎯 [NestedComment] 需要加载更多三层回复，触发load-sub-comments事件`
          );
          this.$emit("load-sub-comments", replyId);
        } else {
          console.log(`🎯 [NestedComment] 所有三层回复已加载，直接展开显示`);
        }
      }

      // 强制更新视图
      this.$forceUpdate();
    },

    // 获取顶级评论的总回复数量（优先使用后端返回的replyCount）
    getTotalRepliesCount(topComment) {
      // 优先使用后端返回的replyCount字段，这是真实的总数量
      if (
        topComment.replyCount !== undefined &&
        topComment.replyCount !== null
      ) {
        console.log(
          `🎯 [NestedComment] 顶级评论${topComment.commentId}使用后端replyCount:`,
          topComment.replyCount
        );
        return topComment.replyCount;
      }

      // 降级方案：计算当前已加载的回复数量（包括二层和三层回复）
      const level2Replies = this.commentList.filter(
        (comment) => comment.parentId === topComment.commentId
      );

      let totalCount = level2Replies.length;

      // 计算三层回复数量
      level2Replies.forEach((level2Reply) => {
        const level3Replies = this.commentList.filter(
          (comment) => comment.parentId === level2Reply.commentId
        );
        totalCount += level3Replies.length;
      });

      console.log(
        `🎯 [NestedComment] 顶级评论${topComment.commentId}计算得出总回复数量:`,
        totalCount
      );
      return totalCount;
    },

    // 获取要显示的回复列表（按需加载模式）
    getDisplayReplies(comment) {
      const replies = comment.replies || [];
      const commentId = comment.commentId || comment.replyId;

      // 检查是否已展开（在foldedComments集合中表示已展开）
      const isExpanded = this.foldedComments.has(commentId);
      console.log(
        `🎯 [NestedComment] 评论${commentId}展开状态检查:`,
        isExpanded,
        "foldedComments:",
        Array.from(this.foldedComments)
      );

      // 如果没有回复数据但有回复数量，说明需要按需加载
      if (replies.length === 0 && comment.replyCount > 0) {
        console.log(
          `🎯 [NestedComment] 评论${commentId}需要按需加载回复，回复数量:`,
          comment.replyCount
        );
        // 如果已展开但没有数据，触发加载
        if (isExpanded && !this.loadingReplies.has(commentId)) {
          this.loadSubComments(commentId);
        }
        return [];
      }

      // 检查是否包含目标评论（智能跳转场景）
      if (
        this.highlightedCommentId &&
        replies.some(
          (reply) =>
            (reply.commentId || reply.replyId) == this.highlightedCommentId
        )
      ) {
        console.log(
          `🎯 [NestedComment] 评论${commentId}包含目标评论${this.highlightedCommentId}，回复数量:`,
          replies.length
        );
        console.log(`🎯 [NestedComment] 评论${commentId}展开状态:`, isExpanded);

        // 强制展开包含目标评论的评论，并强制触发视图更新
        if (!isExpanded) {
          console.log(
            `🎯 [NestedComment] 强制展开评论${commentId}以显示目标评论${this.highlightedCommentId}`
          );
          this.foldedComments.add(commentId);
        }
        console.log(
          `🎯 [NestedComment] 智能跳转：返回全部${replies.length}条回复`
        );
        return replies;
      }

      // 如果已展开，显示全部回复
      if (isExpanded) {
        console.log(
          `🎯 [NestedComment] 评论${commentId}已展开，显示全部${replies.length}条回复`
        );
        return replies;
      }

      // 如果总回复数量小于等于3条，全部显示
      if (comment.replyCount <= 3) {
        console.log(
          `🎯 [NestedComment] 评论${commentId}总回复数量<=3，显示全部:`,
          replies.length
        );
        return replies;
      }

      // 默认折叠状态，只显示前3条（与后端保持一致）
      console.log(
        `🎯 [NestedComment] 评论${commentId}折叠状态，只显示前3条回复`
      );
      return replies.slice(0, 3);
    },

    // 获取要显示的子回复列表（根据折叠状态）
    getDisplaySubReplies(reply) {
      const subReplies = reply.replies || [];
      const replyId = reply.commentId || reply.replyId;

      // 检查是否已展开（在foldedSubReplies集合中表示已展开）
      const isExpanded = this.foldedSubReplies.has(replyId);
      console.log(
        `🎯 [NestedComment] 回复${replyId}子回复展开状态检查:`,
        isExpanded,
        "foldedSubReplies:",
        Array.from(this.foldedSubReplies)
      );

      // 检查是否包含目标评论（智能跳转场景）
      if (
        this.highlightedCommentId &&
        subReplies.some(
          (sub) => (sub.commentId || sub.replyId) == this.highlightedCommentId
        )
      ) {
        console.log(
          `🎯 [NestedComment] 回复${replyId}包含目标评论${this.highlightedCommentId}，子回复数量:`,
          subReplies.length
        );
        console.log(`🎯 [NestedComment] 回复${replyId}展开状态:`, isExpanded);

        // 强制展开包含目标评论的回复
        if (!isExpanded) {
          console.log(
            `🎯 [NestedComment] 强制展开回复${replyId}以显示目标评论${this.highlightedCommentId}`
          );
          this.foldedSubReplies.add(replyId);
          return subReplies;
        }
      }

      // 如果已展开，显示全部子回复
      if (isExpanded) {
        console.log(
          `🎯 [NestedComment] 回复${replyId}子回复已展开，显示全部${subReplies.length}条`
        );
        return subReplies;
      }

      // 如果子回复数量小于等于2条，全部显示
      if (subReplies.length <= 2) {
        console.log(
          `🎯 [NestedComment] 回复${replyId}子回复数量<=2，显示全部:`,
          subReplies.length
        );
        return subReplies;
      }

      // 默认折叠状态，只显示前2条（与主回复逻辑保持一致）
      console.log(
        `🎯 [NestedComment] 回复${replyId}子回复折叠状态，只显示前2条`
      );
      return subReplies.slice(0, 2);
    },

    // 获取顶级评论折叠状态（true表示折叠，false表示展开）
    getTopFoldState() {
      // 计算真实的顶级评论数量
      const topCommentsCount = this.commentList.filter(
        (comment) => !comment.parentId || comment.parentId === 0
      ).length;

      // 如果顶级评论数量大于5条：默认折叠（topCommentsExpanded为false），展开时设为true
      if (topCommentsCount > 5) {
        return !this.topCommentsExpanded; // false=折叠状态
      }
      return false; // 评论数量<=5条时不显示折叠按钮
    },

    // 获取折叠状态（true表示折叠，false表示展开）
    getFoldState(comment) {
      const commentId = comment.commentId;
      const totalReplies = this.getTotalRepliesCount(comment);

      // 如果总回复数量大于3条：默认折叠（不在集合中），展开时加入集合
      if (totalReplies > 3) {
        const isExpanded = this.foldedComments.has(commentId);
        console.log(
          `🎯 [NestedComment] 评论${commentId}折叠状态检查: 总回复${totalReplies}条, 展开状态:${isExpanded}`
        );
        return !isExpanded; // 不在集合中=折叠状态
      }
      return false; // 总回复数量<=3条时不显示折叠按钮
    },

    // 获取子回复折叠状态（true表示折叠，false表示展开）
    getSubFoldState(reply) {
      const replyId = reply.commentId || reply.replyId;
      // 如果子回复数量大于2条：默认折叠（不在集合中），展开时加入集合
      if (reply.replies && reply.replies.length > 2) {
        return !this.foldedSubReplies.has(replyId); // 不在集合中=折叠状态
      }
      return false; // 子回复数量<=2条时不显示折叠按钮
    },

    // 切换顶级评论折叠状态
    toggleTopCommentsFold() {
      this.topCommentsExpanded = !this.topCommentsExpanded;
      // 强制更新视图
      this.$forceUpdate();
    },

    // 展开所有顶级评论（用于智能跳转）
    expandAllTopComments() {
      console.log("🎯 [NestedComment] 开始展开所有顶级评论");
      console.log(
        "🎯 [NestedComment] 展开前topCommentsExpanded:",
        this.topCommentsExpanded
      );
      console.log("🎯 [NestedComment] 评论总数:", this.commentList.length);

      // 设置智能跳转标志，防止状态被重置
      this.isExpandingForTarget = true;

      // 强制触发响应式更新
      this.$set(this, "topCommentsExpanded", true);

      console.log(
        "🎯 [NestedComment] 展开后topCommentsExpanded:",
        this.topCommentsExpanded
      );
      console.log("🎯 [NestedComment] 顶级评论展开完成");
    },

    // 展开所有回复（用于智能跳转）
    expandAllReplies() {
      console.log("🎯 [NestedComment] 开始展开所有回复");
      console.log("🎯 [NestedComment] 当前评论数量:", this.commentList.length);
      console.log(
        "🎯 [NestedComment] 展开前foldedComments:",
        Array.from(this.foldedComments)
      );

      // 设置智能跳转标志，防止状态被重置
      this.isExpandingForTarget = true;

      // 将所有评论的回复都标记为展开状态
      this.commentList.forEach((comment, index) => {
        const commentId = comment.commentId || comment.replyId;
        this.foldedComments.add(commentId); // 加入集合表示展开
        console.log(
          `🎯 [NestedComment] 展开评论${index}:`,
          commentId,
          "回复数量:",
          comment.replies?.length || 0
        );
      });

      console.log(
        "🎯 [NestedComment] 展开后foldedComments:",
        Array.from(this.foldedComments)
      );
      console.log("🎯 [NestedComment] 回复展开完成");

      // 强制触发视图更新
      this.$forceUpdate();
    },

    // 展开所有子回复（用于智能跳转）
    expandAllSubReplies() {
      console.log("🎯 [NestedComment] 开始展开所有子回复");
      console.log(
        "🎯 [NestedComment] 展开前foldedSubReplies:",
        Array.from(this.foldedSubReplies)
      );

      // 设置智能跳转标志，防止状态被重置
      this.isExpandingForTarget = true;

      // 将所有回复的子回复都标记为展开状态
      this.commentList.forEach((comment) => {
        if (comment.replies) {
          comment.replies.forEach((reply) => {
            const replyId = reply.commentId || reply.replyId;
            this.foldedSubReplies.add(replyId); // 加入集合表示展开
          });
        }
      });

      console.log(
        "🎯 [NestedComment] 展开后foldedSubReplies:",
        Array.from(this.foldedSubReplies)
      );
      console.log("🎯 [NestedComment] 子回复展开完成");

      // 强制触发视图更新
      this.$forceUpdate();
    },

    // 高亮显示指定评论（用于智能跳转）
    highlightComment(commentId) {
      console.log(
        "🚨 [紧急调试] highlightComment方法被调用了！评论ID:",
        commentId
      );
      console.log(
        "🎯 [智能跳转] NestedComment组件收到高亮请求，评论ID:",
        commentId
      );

      // 设置高亮状态
      this.highlightedCommentId = commentId;

      // 调试：检查评论95的确切位置
      console.log("🔍 [调试] 开始查找评论", commentId, "的确切位置");
      console.log("🔍 [调试] 当前评论列表长度:", this.commentList.length);

      // 直接查找评论98
      if (commentId == 98) {
        console.log("🔍 [调试] 查找评论98");
        for (let i = 0; i < this.commentList.length; i++) {
          const comment = this.commentList[i];
          if (comment.commentId == 48) {
            console.log(
              "🔍 [调试] 找到评论48，回复数量:",
              comment.replies?.length
            );
            if (comment.replies) {
              for (let j = 0; j < comment.replies.length; j++) {
                const reply = comment.replies[j];
                console.log(
                  `🔍 [调试] 回复${j}: commentId=${reply.commentId}, replyId=${reply.replyId}`
                );
                if (reply.commentId == 98 || reply.replyId == 98) {
                  console.log(
                    "🔍 [调试] 找到评论98！实际ID:",
                    reply.commentId || reply.replyId
                  );
                  console.log(
                    "🔍 [调试] DOM ID应该是: comment-" +
                      (reply.commentId || reply.replyId)
                  );
                }
              }
            }
            break;
          }
        }
      }

      // 强制触发视图更新
      this.$forceUpdate();

      // 使用页面滚动到目标评论，尝试多种可能的ID
      this.$nextTick(() => {
        setTimeout(() => {
          // 尝试多种可能的ID格式
          const possibleSelectors = [
            `#comment-${commentId}`,
            `#comment-${commentId}`,
            `.comment-${commentId}`,
            `[id="comment-${commentId}"]`,
          ];

          console.log("🎯 [智能跳转] 尝试滚动到评论:", commentId);
          console.log("🎯 [智能跳转] 可能的选择器:", possibleSelectors);

          // 尝试第一个选择器
          uni.pageScrollTo({
            selector: possibleSelectors[0],
            duration: 500,
          });
          console.log("🎯 [智能跳转] 使用pageScrollTo滚动到评论:", commentId);
        }, 1000); // 减少延迟时间
      });

      // 3秒后取消高亮
      setTimeout(() => {
        this.highlightedCommentId = null;
      }, 3000);
    },

    // 调试方法：检查评论的确切位置
    debugCommentLocation(targetCommentId) {
      console.log("🔍 [调试] 开始查找评论", targetCommentId, "的确切位置");

      for (let i = 0; i < this.commentList.length; i++) {
        const comment = this.commentList[i];
        const commentId = comment.commentId || comment.replyId;

        // 检查顶级评论
        if (commentId == targetCommentId) {
          console.log(
            "🔍 [调试] 评论",
            targetCommentId,
            "是顶级评论，索引:",
            i
          );
          return;
        }

        // 检查二级回复
        if (comment.replies) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            const replyId = reply.commentId || reply.replyId;

            if (replyId == targetCommentId) {
              console.log(
                "🔍 [调试] 评论",
                targetCommentId,
                "是评论",
                commentId,
                "的第",
                j,
                "个二级回复"
              );
              console.log(
                "🔍 [调试] 评论",
                targetCommentId,
                "的实际ID:",
                replyId,
                "commentId:",
                reply.commentId,
                "replyId:",
                reply.replyId
              );
              console.log(
                "🔍 [调试] 评论",
                targetCommentId,
                "的DOM ID应该是: comment-" + (reply.commentId || reply.replyId)
              );
              return;
            }

            // 检查三级回复
            if (reply.replies) {
              for (let k = 0; k < reply.replies.length; k++) {
                const subReply = reply.replies[k];
                const subReplyId = subReply.commentId || subReply.replyId;

                if (subReplyId == targetCommentId) {
                  console.log(
                    "🔍 [调试] 评论",
                    targetCommentId,
                    "是回复",
                    replyId,
                    "的第",
                    k,
                    "个三级回复"
                  );
                  return;
                }
              }
            }
          }
        }
      }

      console.log("🔍 [调试] 未找到评论", targetCommentId, "的位置");
    },

    // 切换回复折叠状态（三层楼中楼版本）
    async toggleRepliesFold(comment) {
      const commentId = comment.commentId;
      const totalReplies = this.getTotalRepliesCount(comment);

      console.log(
        `🎯 [NestedComment] 切换评论${commentId}的折叠状态，总回复数量:${totalReplies}`
      );

      if (this.foldedComments.has(commentId)) {
        // 当前是展开状态，点击后折叠（从集合中移除）
        this.foldedComments.delete(commentId);
        console.log(`🎯 [NestedComment] 折叠评论${commentId}`);
      } else {
        // 当前是折叠状态，点击后展开（加入集合）
        this.foldedComments.add(commentId);
        console.log(`🎯 [NestedComment] 展开评论${commentId}`);

        // 如果还有更多回复没有加载，触发加载更多回复
        const currentLevel2Count = this.commentList.filter(
          (c) => c.parentId === commentId
        ).length;

        if (totalReplies > currentLevel2Count) {
          console.log(
            `🎯 [NestedComment] 展开时触发加载更多回复，父评论ID: ${commentId}, 当前已加载: ${currentLevel2Count}, 总数: ${totalReplies}`
          );
          this.$emit("load-more-replies", commentId);
        }
      }

      // 强制更新视图
      this.$forceUpdate();
    },

    // 按需加载子回复（新的按需加载方法）
    async loadSubComments(commentId, silent = false) {
      if (!silent) {
        console.log("🔄 [按需加载] 加载评论的子回复:", commentId);
      }

      // 防止重复加载
      if (this.loadingReplies.has(commentId)) {
        console.log("🔄 [按需加载] 评论正在加载中，跳过:", commentId);
        return;
      }

      try {
        // 标记为正在加载
        this.loadingReplies.add(commentId);

        // 通过emit通知父组件加载子回复
        this.$emit("load-sub-comments", commentId);

        // 标记为已加载
        this.loadedReplies.add(commentId);
      } catch (error) {
        console.error("❌ [按需加载] 加载子评论失败:", error);
      } finally {
        // 移除加载状态
        this.loadingReplies.delete(commentId);
      }
    },

    // 按需加载子回复（兼容旧方法）
    async loadMoreReplies(commentId) {
      try {
        console.log(`🔄 [NestedComment] 开始加载评论${commentId}的子回复...`);

        // 标记为已加载
        this.loadedReplies.add(commentId);

        // 通过emit通知父组件加载更多回复
        this.$emit("load-more-replies", commentId);
      } catch (error) {
        console.error("❌ [NestedComment] 加载子回复异常:", error);
      }
    },

    // 切换子回复折叠状态
    toggleSubRepliesFold(reply) {
      const replyId = reply.commentId || reply.replyId;

      if (this.foldedSubReplies.has(replyId)) {
        // 当前是展开状态，点击后折叠（从集合中移除）
        this.foldedSubReplies.delete(replyId);
      } else {
        // 当前是折叠状态，点击后展开（加入集合）
        this.foldedSubReplies.add(replyId);
      }

      // 强制更新视图
      this.$forceUpdate();
    },

    // 显示回复输入框（三层楼中楼设计）
    showReplyInput(comment, topComment = null) {
      const commentId = comment.commentId;

      // 根据三层楼中楼设计确定评论类型和层级关系
      let parentId, rootId, commentType;

      if (topComment) {
        // 回复二级评论：type=3（回复子评论）
        // topComment是顶级评论，comment是被回复的二级评论
        rootId = topComment.commentId || topComment.replyId;
        parentId = rootId; // 扁平化：父ID设为根评论ID
        commentType = 3;
      } else {
        // 回复顶级评论：type=2（回复评论）
        rootId = commentId;
        parentId = commentId;
        commentType = 2;
      }

      this.showReplyFor = rootId;
      this.replyTarget = {
        commentId: commentId,
        userId: comment.userId,
        userName: comment.nickName,
        parentId: parentId,
        rootId: rootId,
        type: commentType,
        replyToUserId: comment.userId,
        replyToUserName: comment.nickName,
        isReplyToReply: !!topComment,
      };
      this.replyPlaceholder = `回复 @${comment.nickName}:`;
      this.replyText = "";
    },

    // 隐藏回复输入框
    hideReplyInput() {
      this.showReplyFor = null;
      this.replyTarget = null;
      this.replyText = "";
      this.replyPlaceholder = "写下你的回复...";
    },

    // 提交主评论
    async submitMainComment() {
      if (!this.mainCommentText.trim()) return;

      this.$emit("submit-comment", {
        content: this.mainCommentText,
        type: "main",
      });

      this.mainCommentText = "";
    },

    // 提交回复（匹配数据库字段设计）
    async submitReply() {
      if (!this.replyText.trim() || !this.replyTarget) return;

      this.$emit("submit-reply", {
        content: this.replyText,
        parentId: this.replyTarget.parentId,
        rootId: this.replyTarget.rootId,
        type: this.replyTarget.type,
        replyToUserId: this.replyTarget.replyToUserId,
        replyToUserName: this.replyTarget.replyToUserName,
        originalCommentId: this.replyTarget.commentId,
      });

      this.hideReplyInput();
    },

    // 点赞评论
    async likeComment(comment) {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        // 发出点赞事件给父组件处理
        this.$emit("like-comment", {
          commentId: comment.commentId || comment.replyId,
          userId: userData.userId,
          comment: comment,
        });
      } catch (error) {
        console.error("点赞评论异常:", error);
        uni.showToast({
          title: "操作失败",
          icon: "none",
        });
      }
    },

    // ========== 智能跳转相关方法 ==========

    // 智能跳转预加载
    async preloadForSmartJump() {
      if (!this.highlightedCommentId) return;

      console.log(
        "🎯 [智能跳转] 开始预加载目标评论:",
        this.highlightedCommentId
      );

      try {
        // 查找目标评论的路径
        const commentPath = await this.findCommentPath(
          this.highlightedCommentId
        );

        if (commentPath && commentPath.parentCommentId) {
          // 目标是子评论，需要预加载父评论的所有子评论
          console.log(
            "🎯 [智能跳转] 目标是子评论，预加载父评论:",
            commentPath.parentCommentId
          );
          await this.loadSubComments(commentPath.parentCommentId, true); // true表示静默加载

          // 自动展开父评论
          this.foldedComments.add(commentPath.parentCommentId);
        }

        // 等待DOM更新后滚动到目标
        this.$nextTick(() => {
          this.scrollToTargetComment();
        });
      } catch (error) {
        console.error("🎯 [智能跳转] 预加载失败:", error);
        // 降级方案：尝试直接滚动
        this.scrollToTargetComment();
      }
    },

    // 滚动到目标评论
    scrollToTargetComment() {
      if (!this.highlightedCommentId) return;

      const commentId = this.highlightedCommentId;
      console.log("🎯 [智能跳转] 开始滚动到目标评论:", commentId);

      // 等待DOM更新
      this.$nextTick(() => {
        setTimeout(() => {
          // 尝试多种可能的ID格式
          const possibleSelectors = [
            `#comment-${commentId}`,
            `.comment-${commentId}`,
            `[id="comment-${commentId}"]`,
          ];

          console.log("🎯 [智能跳转] 尝试滚动到评论:", commentId);
          console.log("🎯 [智能跳转] 可能的选择器:", possibleSelectors);

          // 尝试第一个选择器
          uni.pageScrollTo({
            selector: possibleSelectors[0],
            duration: 500,
          });
          console.log("🎯 [智能跳转] 使用pageScrollTo滚动到评论:", commentId);
        }, 500);
      });

      // 3秒后取消高亮
      setTimeout(() => {
        console.log("🎯 [智能跳转] 取消高亮显示");
        this.highlightedCommentId = null;
        this.smartJumpMode = false;
      }, 3000);
    },

    // 查找评论路径（模拟后端API）
    async findCommentPath(commentId) {
      // 先在当前已加载的评论中查找
      for (const comment of this.commentList) {
        const topCommentId = comment.commentId || comment.replyId;

        // 检查是否是顶级评论
        if (topCommentId == commentId) {
          return {
            commentId: commentId,
            isSubComment: false,
            parentCommentId: null,
          };
        }

        // 检查是否在子回复中
        if (comment.replies) {
          for (const reply of comment.replies) {
            const replyId = reply.commentId || reply.replyId;
            if (replyId == commentId) {
              return {
                commentId: commentId,
                isSubComment: true,
                parentCommentId: topCommentId,
              };
            }
          }
        }
      }

      // 如果在当前数据中没找到，可能需要从服务器获取
      // 这里可以调用后端API来查找评论路径
      console.log(
        "🎯 [智能跳转] 在当前数据中未找到目标评论，可能需要从服务器加载"
      );
      return null;
    },

    // 显示输入框提示（不聚焦，避免移动端灰屏问题）
    focusInput() {
      console.log("✅ NestedComment: 开始显示高亮提示");

      // 只显示高亮提示，不做任何聚焦操作
      this.shouldHighlightInput = true;

      // 强制更新视图
      this.$forceUpdate();

      console.log("✅ NestedComment: 高亮提示已激活，3秒后自动消失");

      // 几秒后自动移除高亮提示
      setTimeout(() => {
        console.log("⏰ NestedComment: 移除高亮提示");
        this.shouldHighlightInput = false;
      }, 3000);
    },
  },
};
</script>

<style scoped>
.nested-comment-container {
  padding: 20rpx;
}

.comment-section-header {
  margin-bottom: 30rpx;
}

.comment-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.no-comments {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
}

.no-comments-text {
  display: block;
  margin-top: 20rpx;
  font-size: 26rpx;
}

.top-comment {
  margin-bottom: 0;
  padding: 20rpx;
  background: #fff;
  border-radius: 0;
  border-bottom: 1rpx solid #e0e0e0;
  position: relative;
}

.top-comment:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.author-badge {
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  box-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.3);
}

.comment-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.comment-actions {
  display: flex;
  align-items: center;
}

.like-btn,
.reply-btn {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.like-btn .liked {
  color: #ff4757;
  font-weight: bold;
}

.comment-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 20rpx;
}

.replies-container {
  margin-left: 30rpx;
  margin-top: 12rpx;
  position: relative;
}

.reply-item {
  margin-bottom: 0;
  padding: 16rpx;
  background: transparent;
  border-radius: 0;
  border: none;
  position: relative;
}

/* 三层楼中楼样式 */
.level2-reply-container {
  margin-bottom: 16rpx;
}

.level2-reply {
  margin-left: 40rpx; /* 二层回复缩进 */
  padding: 16rpx 20rpx;
  border-left: 3rpx solid #e8f4fd;
  background: rgba(232, 244, 253, 0.3);
  border-radius: 0 12rpx 12rpx 0;
}

.level3-replies-container {
  margin-top: 16rpx;
}

.level3-reply {
  margin-left: 40rpx; /* 三层回复在二层基础上再缩进 */
  padding: 16rpx 20rpx;
  border-left: 3rpx solid #ffeaa7;
  background: rgba(255, 234, 167, 0.2);
  border-radius: 0 12rpx 12rpx 0;
}

/* 顶级评论和第一个回复之间的横线 */
.top-reply-separator {
  height: 1rpx;
  background: #e0e0e0;
  margin: 16rpx 0; /* 不延伸到边界，保持正常宽度 */
}

/* 加载回复状态 */
.loading-replies {
  padding: 20rpx 0;
  text-align: center;
}

/* 横线分隔 */
.reply-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 0; /* 从回复内容的左边开始 */
  right: 0; /* 到回复内容的右边结束 */
  bottom: 0;
  height: 1rpx;
  background: #e0e0e0;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.reply-avatar {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
  box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.1);
}

.reply-user-info {
  flex: 1;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.reply-username {
  color: #333;
  font-weight: 600;
  margin-right: 8rpx;
}

.reply-username::after {
  content: "•";
  color: #007aff;
  margin-left: 8rpx;
  font-weight: bold;
}

.reply-content {
  font-size: 26rpx;
  line-height: 1.5;
  color: #333;
  margin-bottom: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8rpx;
}

.reply-time {
  font-size: 22rpx;
  color: #666;
  font-style: italic;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.reply-actions .like-btn,
.reply-actions .reply-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  background: transparent;
  transition: all 0.3s ease;
  font-size: 22rpx;
  color: #666;
}

.reply-actions .like-btn:active,
.reply-actions .reply-btn:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

.reply-actions .like-btn .liked {
  color: #ff4757;
  font-weight: bold;
}

/* 子回复样式 */
.sub-replies-container {
  background: #f0f0f0;
  border-radius: 10rpx;
  padding: 16rpx;
  margin-top: 12rpx;
  margin-left: 20rpx;
  position: relative;
}

.sub-replies-container::before {
  content: "";
  position: absolute;
  left: -20rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 10rpx solid transparent;
  border-right-color: #007aff;
}

.sub-reply-item {
  margin-bottom: 16rpx;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  border: 1rpx solid rgba(0, 122, 255, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.sub-reply-item:hover {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
  transform: translateY(-2rpx);
}

.sub-reply-item:last-child {
  margin-bottom: 0;
}

.sub-reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.sub-reply-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 10rpx;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
  box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.1);
}

.sub-reply-user-info {
  flex: 1;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  min-width: 0;
}

.sub-reply-username {
  color: #333;
  font-weight: 600;
  margin-right: 8rpx;
}

.sub-reply-username::after {
  content: "•";
  color: #007aff;
  margin-left: 8rpx;
  font-weight: bold;
}

.reply-target {
  color: #666;
  font-size: 22rpx;
  background: rgba(0, 122, 255, 0.08);
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.sub-reply-content {
  font-size: 22rpx;
  line-height: 1.4;
  color: #333;
  margin-bottom: 6rpx;
  padding: 8rpx 12rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 6rpx;
  word-wrap: break-word;
  word-break: break-all;
}

.sub-reply-time {
  font-size: 18rpx;
  color: #666;
  font-style: italic;
}

.sub-reply-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.sub-reply-actions .like-btn,
.sub-reply-actions .reply-btn {
  display: flex;
  align-items: center;
  padding: 6rpx 10rpx;
  border-radius: 16rpx;
  background: transparent;
  transition: all 0.3s ease;
  font-size: 18rpx;
  color: #666;
}

.sub-reply-actions .like-btn:active,
.sub-reply-actions .reply-btn:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

.sub-reply-actions .like-btn .liked {
  color: #ff4757;
  font-weight: bold;
}

/* 顶级评论折叠按钮样式 */
.top-fold-toggle-btn {
  text-align: center;
  padding: 30rpx 0;
  border-top: 1rpx solid #eee;
  margin-top: 30rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.top-fold-toggle-btn:active {
  background: #f0f0f0;
  border-radius: 10rpx;
}

.top-fold-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007aff;
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.top-fold-content:hover {
  background: rgba(0, 122, 255, 0.1);
}

.top-fold-text {
  margin-left: 10rpx;
  font-weight: 500;
}

/* 折叠/展开按钮样式 */
.fold-toggle-btn {
  text-align: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
  margin-top: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fold-toggle-btn:active {
  background: #f0f0f0;
  border-radius: 8rpx;
}

.fold-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007aff;
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.fold-content:hover {
  background: rgba(0, 122, 255, 0.1);
}

.fold-text {
  margin-left: 8rpx;
  font-weight: 500;
}

/* 子回复折叠按钮样式 */
.sub-fold-toggle-btn {
  text-align: center;
  padding: 24rpx 0;
  border-top: 1rpx solid rgba(0, 122, 255, 0.1);
  margin-top: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.sub-fold-toggle-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #007aff, transparent);
}

.sub-fold-toggle-btn:active {
  background: rgba(0, 122, 255, 0.03);
  border-radius: 12rpx;
}

.sub-fold-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007aff;
  font-size: 26rpx;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 249, 250, 0.9) 100%
  );
  border: 1rpx solid rgba(0, 122, 255, 0.15);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.08);
}

.sub-fold-content:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 248, 255, 0.95) 100%
  );
  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.12);
}

.sub-fold-text {
  margin-left: 10rpx;
  font-weight: 500;
}

.reply-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.reply-input {
  flex: 1;
  padding: 12rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 20rpx;
  margin-right: 12rpx;
}

.submit-reply-btn,
.cancel-reply-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-left: 8rpx;
}

.submit-reply-btn {
  background: #007aff;
  color: white;
}

.cancel-reply-btn {
  background: #f0f0f0;
  color: #666;
}

.main-comment-input {
  position: relative;
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}

.comment-input {
  flex: 1;
  padding: 16rpx 24rpx;
  border: 1rpx solid #ddd;
  border-radius: 30rpx;
  margin-right: 16rpx;
}

.submit-btn {
  padding: 16rpx 32rpx;
  background: #007aff;
  color: white;
  border-radius: 30rpx;
  font-size: 28rpx;
}

.submit-btn:disabled {
  background: #ccc;
}

/* 输入框高亮提示样式 */
.highlight-input {
  animation: input-highlight 1s ease-in-out infinite;
  position: relative;
  border: 2rpx solid #007aff !important;
  border-radius: 10rpx !important;
}

.input-tip {
  position: absolute;
  top: -50rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #007aff;
  color: white;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
  z-index: 100;
  animation: tip-bounce 1s ease-in-out infinite;
}

.input-tip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 8rpx solid transparent;
  border-top-color: #007aff;
}

@keyframes input-highlight {
  0% {
    background: #fff;
    border-color: #007aff;
    box-shadow: 0 0 10rpx rgba(0, 122, 255, 0.3);
    transform: scale(1);
  }
  50% {
    background: #e6f3ff;
    border-color: #ff4757;
    box-shadow: 0 0 25rpx rgba(255, 71, 87, 0.6);
    transform: scale(1.02);
  }
  100% {
    background: #fff;
    border-color: #007aff;
    box-shadow: 0 0 10rpx rgba(0, 122, 255, 0.3);
    transform: scale(1);
  }
}

@keyframes tip-bounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10rpx);
  }
}

/* 高亮目标评论样式 */
.highlight-comment {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
  border: 2rpx solid #ffc107 !important;
  border-radius: 16rpx !important;
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}
</style>
