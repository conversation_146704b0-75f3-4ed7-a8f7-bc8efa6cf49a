<template>
  <view class="goods-detail">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more
        status="loading"
        :content-text="loadingText"
      ></uni-load-more>
    </view>

    <!-- 商品内容 -->
    <view v-else class="goods-main">
      <!-- 左侧图片区 -->
      <view class="goods-left">
        <swiper
          class="goods-swiper"
          :indicator-dots="true"
          :autoplay="true"
          :interval="3000"
        >
          <swiper-item v-for="(img, idx) in goods.images" :key="idx">
            <image
              :src="img"
              class="goods-img"
              mode="aspectFill"
              @click.stop="previewGoodsImages(idx)"
            />
          </swiper-item>
        </swiper>
      </view>
      <!-- 右侧信息区 -->
      <view class="goods-right">
        <view class="goods-info">
          <view class="goods-title">{{ goods.name }}</view>
          <view class="goods-price-box">
            <view class="price-row" v-if="displayPrice.showVipTag">
              <text class="member-price" :key="`vip-${displayPrice.mainPrice}`"
                >￥{{ displayPrice.mainPrice }}</text
              >
              <text class="member-tag">会员价</text>
            </view>
            <text
              v-if="displayPrice.showVipTag"
              class="origin-price"
              :key="`origin-${displayPrice.originalPrice}`"
              >￥{{ displayPrice.originalPrice }}</text
            >
            <text
              v-else
              class="member-price"
              :key="`normal-${displayPrice.mainPrice}`"
              >￥{{ displayPrice.mainPrice }}</text
            >
            <text v-if="goods.promotion" class="promo-tag">{{
              goods.promotion
            }}</text>
          </view>
          <!-- 当前SKU库存信息 -->
          <view class="sku-info" v-if="currentSku">
            <view class="sku-row">
              <text class="sku-stock">库存: {{ currentStock }} 件</text>
            </view>
            <view class="sku-row">
              <text class="sku-desc"
                >已选: {{ formatSpecDesc(currentSku.specDesc) }}</text
              >
            </view>
            <view class="sku-row">
              <text class="sku-price"
                >价格: ￥{{ currentSku.price }}
                {{
                  currentSku.vipPrice
                    ? `| 会员价: ￥${currentSku.vipPrice}`
                    : ""
                }}</text
              >
            </view>
          </view>
          <!-- 未选择SKU时的提示 -->
          <view class="sku-tip" v-else-if="availableSpecs.length > 0">
            <view class="sku-row">
              <text class="tip-text">请选择商品规格</text>
            </view>
          </view>
          <!-- 没有SKU时的商品信息 -->
          <view class="sku-info" v-else-if="skus.length === 0">
            <view class="sku-row">
              <text class="sku-stock">库存: {{ currentStock }} 件</text>
            </view>
            <view class="sku-row">
              <text class="sku-desc">商品价格</text>
            </view>
            <view class="sku-row">
              <text class="sku-price"
                >价格: ￥{{ goods.price }}
                {{
                  goods.vipPrice ? `| 会员价: ￥${goods.vipPrice}` : ""
                }}</text
              >
            </view>
          </view>
          <!-- 购买数量选择器 -->
          <view class="purchase-section">
            <view class="purchase-row">
              <text class="purchase-label">购买数量</text>
              <view class="quantity-control">
                <view
                  class="quantity-btn minus"
                  :class="{ disabled: amount <= 1 }"
                  @click="changeAmount(-1)"
                >
                  <text class="btn-text">-</text>
                </view>
                <view class="quantity-input">
                  <input
                    type="number"
                    v-model="amount"
                    class="input-field"
                    :min="1"
                    :max="maxStock"
                    @input="onAmountInput"
                    @blur="validateAmount"
                    @focus="onAmountFocus"
                  />
                </view>
                <view
                  class="quantity-btn plus"
                  :class="{ disabled: amount >= maxStock }"
                  @click="changeAmount(1)"
                >
                  <text class="btn-text">+</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="goods-attrs" v-if="availableSpecs.length > 0">
          <view
            class="attr-row"
            v-for="spec in availableSpecs"
            :key="spec.name"
          >
            <text class="attr-name">{{ spec.name }}：</text>
            <view class="attr-values">
              <text
                v-for="value in spec.values"
                :key="value"
                :class="[
                  'attr-value',
                  { active: selectedSpecs[spec.name] === value },
                ]"
                @click="selectSpec(spec.name, value)"
                >{{ value }}</text
              >
            </view>
          </view>
        </view>
        <view class="goods-actions">
          <button class="cart-btn" @click="addToCart">加入购物车</button>
          <button class="buy-btn" @click="buyNow">立即购买</button>
        </view>
      </view>
    </view>
    <!-- 详情介绍 -->
    <view v-if="!loading" class="goods-desc">
      <view class="desc-title">{{ goods.name }}</view>
      <view class="desc-subtitle">{{ goods.subtitle }}</view>
      <view class="desc-text">{{ goods.desc }}</view>

      <view class="desc-section">
        <view class="section-title">产品特点</view>
        <ul>
          <li v-for="(feature, idx) in goodsFeatures" :key="idx">
            {{ feature }}
          </li>
        </ul>
      </view>

      <view class="desc-section">
        <view class="section-title">产品参数</view>
        <view v-for="(val, key) in goodsSpecs" :key="key">
          <text class="spec-key">{{ key }}：</text>
          <text class="spec-val">{{ val }}</text>
        </view>
      </view>

      <view class="desc-section">
        <view class="section-title">产品图片</view>
        <image
          v-for="(img, idx) in goods.images"
          :key="idx"
          :src="img"
          style="width: 100%; margin-bottom: 10px"
          mode="widthFix"
          @click.stop="previewGoodsImages(idx)"
        />
      </view>

      <view class="desc-section">
        <view class="section-title">使用说明</view>
        <view>{{ goodsUsage }}</view>
      </view>

      <view class="desc-section">
        <view class="section-title">温馨提示</view>
        <view>{{ goodsTips }}</view>
      </view>

      <view class="desc-section">
        <view class="section-title">售后服务</view>
        <view>{{ goodsAfterSale }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { apiAddToCart } from "@/shop/api/cart.js";
import { apiGetGoodsDetail } from "@/shop/api/goods.js";

const isVip = ref(true); // 假设当前用户是会员

// 用户信息和商品信息
const currentUser = ref(null);
const goodsId = ref(null);
const currentSku = ref(null);
const loading = ref(false);
const loadingText = reactive({
  contentdown: "上拉显示更多",
  contentrefresh: "正在加载...",
  contentnomore: "没有更多数据了",
});

// 商品数据
const goods = reactive({
  id: null,
  name: "",
  subtitle: "",
  desc: "",
  img: "",
  price: 0,
  vipPrice: 0,
  originPrice: 0,
  promotion: "",
  sales: 0,
  stock: 0,
  images: [],
});

// SKU和规格数据
const skus = ref([]);
const selectedSpecs = reactive({});
const amount = ref(1);

// 计算属性
const currentPrice = computed(() => {
  if (currentSku.value) {
    return {
      price: currentSku.value.price || goods.price,
      vipPrice: currentSku.value.vipPrice || goods.vipPrice,
    };
  }
  return {
    price: goods.price,
    vipPrice: goods.vipPrice,
  };
});

// 当前SKU的库存信息
const currentStock = computed(() => {
  if (currentSku.value) {
    return currentSku.value.stock || 0;
  }
  return goods.stock || 0;
});

// 计算属性
const maxStock = computed(() => {
  return currentStock.value;
});

// 价格显示逻辑
const displayPrice = computed(() => {
  const price = currentPrice.value;
  if (isVip.value && price.vipPrice && price.vipPrice > 0) {
    return {
      mainPrice: price.vipPrice,
      originalPrice: price.price,
      showVipTag: true,
    };
  } else {
    return {
      mainPrice: price.price,
      originalPrice: null,
      showVipTag: false,
    };
  }
});

// 可用的规格选项（只显示该商品实际拥有的SKU对应的规格）
const availableSpecs = computed(() => {
  if (skus.value.length === 0) {
    return [];
  }

  // 从SKU中提取所有规格名称和值
  const specMap = new Map();

  skus.value.forEach((sku) => {
    if (sku.specDesc) {
      // 解析规格描述，格式可能是 "颜色:白色,尺寸:大号" 或 JSON格式
      let specObj = {};
      try {
        // 尝试解析为JSON
        if (sku.specDesc.startsWith("{")) {
          specObj = JSON.parse(sku.specDesc);
        } else {
          // 解析为 "key:value" 格式
          specObj = {};
          sku.specDesc.split(",").forEach((item) => {
            const [key, value] = item.split(":");
            if (key && value) {
              specObj[key.trim()] = value.trim();
            }
          });
        }
      } catch (e) {
        console.warn("解析SKU规格描述失败:", sku.specDesc);
        return;
      }

      // 将规格添加到Map中
      Object.entries(specObj).forEach(([specName, specValue]) => {
        if (!specMap.has(specName)) {
          specMap.set(specName, new Set());
        }
        specMap.get(specName).add(specValue);
      });
    }
  });

  // 转换为数组格式
  return Array.from(specMap.entries()).map(([name, values]) => ({
    name,
    values: Array.from(values),
  }));
});

const goodsFeatures = computed(() => {
  // 根据商品描述生成特点
  const features = [];
  if (goods.desc) {
    features.push(goods.desc);
  }
  if (goods.subtitle) {
    features.push(goods.subtitle);
  }
  return features.length > 0 ? features : ["优质商品，值得信赖"];
});

const goodsSpecs = computed(() => {
  return {
    商品名称: goods.name,
    商品价格: `￥${goods.price}`,
    会员价格: goods.vipPrice ? `￥${goods.vipPrice}` : "暂无",
    库存数量: goods.stock || 0,
    销量: goods.sales || 0,
    促销信息: goods.promotion || "暂无",
  };
});

const goodsUsage = computed(() => {
  return goods.desc || "请按照产品说明书使用。";
});

const goodsTips = computed(() => {
  return "请妥善保管商品，如有问题请联系客服。";
});

const goodsAfterSale = computed(() => {
  return "本产品享受7天无理由退换货，1年质保服务。";
});

// 选择规格
function selectSpec(name, value) {
  selectedSpecs[name] = value;
  updateCurrentSku();
}

// 更新当前选中的SKU
function updateCurrentSku() {
  if (availableSpecs.value.length === 0) {
    currentSku.value = null;
    return;
  }

  // 检查是否所有规格都已选择
  const allSpecsSelected = availableSpecs.value.every(
    (spec) => selectedSpecs[spec.name]
  );

  if (!allSpecsSelected) {
    currentSku.value = null;
    return;
  }

  // 根据选择的规格找到对应的SKU
  const selectedSpecObj = {};
  availableSpecs.value.forEach((spec) => {
    if (selectedSpecs[spec.name]) {
      selectedSpecObj[spec.name] = selectedSpecs[spec.name];
    }
  });

  // 匹配SKU
  currentSku.value =
    skus.value.find((sku) => {
      if (!sku.specDesc) return false;

      try {
        let skuSpecObj = {};
        if (sku.specDesc.startsWith("{")) {
          skuSpecObj = JSON.parse(sku.specDesc);
        } else {
          skuSpecObj = {};
          sku.specDesc.split(",").forEach((item) => {
            const [key, value] = item.split(":");
            if (key && value) {
              skuSpecObj[key.trim()] = value.trim();
            }
          });
        }

        // 比较规格是否匹配
        return (
          Object.keys(selectedSpecObj).every(
            (key) => skuSpecObj[key] === selectedSpecObj[key]
          ) &&
          Object.keys(skuSpecObj).every(
            (key) => selectedSpecObj[key] === skuSpecObj[key]
          )
        );
      } catch (e) {
        return false;
      }
    }) || null;
}

// 修改数量
function changeAmount(delta) {
  const newAmount = amount.value + delta;
  if (newAmount >= 1 && newAmount <= maxStock.value) {
    amount.value = newAmount;
  }
}

// 验证数量输入
function validateAmount() {
  if (amount.value < 1) {
    amount.value = 1;
    // 失去焦点
    uni.hideKeyboard();
  } else if (amount.value > maxStock.value) {
    amount.value = maxStock.value;
    // 失去焦点
    uni.hideKeyboard();
  }
}

// 防抖定时器
let inputTimer = null;

// 监听输入事件，实时验证
function onAmountInput(e) {
  const value = parseInt(e.detail.value);

  // 清除之前的定时器
  if (inputTimer) {
    clearTimeout(inputTimer);
  }

  // 设置新的定时器，延迟验证
  inputTimer = setTimeout(() => {
    if (value > maxStock.value) {
      amount.value = maxStock.value;
      uni.hideKeyboard();
    }
  }, 300); // 300ms延迟
}

// 监听获得焦点事件
function onAmountFocus() {
  if (amount.value > maxStock.value) {
    amount.value = maxStock.value;
  }
}

// 预览商品图片
const previewGoodsImages = (currentIndex = 0) => {
  if (!goods.images || goods.images.length === 0) {
    uni.showToast({
      title: "暂无商品图片",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  uni.previewImage({
    urls: goods.images,
    current: currentIndex,
    success: () => {
      console.log("商品图片预览成功");
    },
    fail: (error) => {
      console.error("商品图片预览失败:", error);
      uni.showToast({
        title: "图片预览失败",
        icon: "none",
        duration: 2000,
      });
    },
  });
};

// 获取商品详情
async function fetchGoodsDetail() {
  if (!goodsId.value) {
    uni.showToast({
      title: "商品ID不能为空",
      icon: "none",
    });
    return;
  }

  loading.value = true;
  try {
    console.log("开始获取商品详情，商品ID:", goodsId.value);
    const response = await apiGetGoodsDetail(goodsId.value);
    console.log("商品详情响应:", response);

    if (response && response.data) {
      const goodsData = response.data;
      console.log("商品数据:", goodsData);

      // 更新商品信息
      Object.assign(goods, {
        id: goodsData.id,
        name: goodsData.name || "",
        subtitle: goodsData.subtitle || "",
        desc: goodsData.desc || "",
        img: goodsData.img || "",
        price: goodsData.price || 0,
        vipPrice: goodsData.vipPrice || 0,
        originPrice: goodsData.originPrice || goodsData.price || 0,
        promotion: goodsData.promotion || "",
        sales: goodsData.sales || 0,
        stock: goodsData.stock || 0,
        images: goodsData.img ? [goodsData.img] : [],
      });

      console.log("更新后的商品信息:", goods);

      // 从商品详情中获取SKU信息
      if (goodsData.shopSkuList) {
        skus.value = goodsData.shopSkuList;
        console.log("SKU列表:", skus.value);
      }

      // 初始化规格选择
      initSpecSelection();
    } else {
      throw new Error("商品数据为空");
    }
  } catch (error) {
    console.error("获取商品详情失败:", error);
    uni.showToast({
      title: "获取商品信息失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
  }
}

// 初始化规格选择
function initSpecSelection() {
  if (availableSpecs.value.length > 0) {
    // 自动选择第一个规格的第一个值
    availableSpecs.value.forEach((spec) => {
      if (spec.values && spec.values.length > 0) {
        selectedSpecs[spec.name] = spec.values[0];
      }
    });
    updateCurrentSku();
  } else if (skus.value.length > 0) {
    // 如果没有规格选项但有SKU，自动选择第一个SKU
    currentSku.value = skus.value[0];
    console.log("自动选择第一个SKU:", currentSku.value);
  }
}

// 添加到购物车
async function addToCart() {
  // 检查用户登录状态
  if (!currentUser.value?.userId) {
    uni.showToast({
      title: "请先登录",
      icon: "none",
    });
    return;
  }

  // 检查是否选择了规格
  if (availableSpecs.value.length > 0 && !currentSku.value) {
    uni.showToast({
      title: "请选择商品规格",
      icon: "none",
    });
    return;
  }

  // 验证购买数量
  if (amount.value < 1) {
    uni.showToast({
      title: "购买数量不能少于1",
      icon: "none",
    });
    return;
  }

  if (amount.value > maxStock.value) {
    uni.showToast({
      title: `库存不足，最多可购买${maxStock.value}件`,
      icon: "none",
    });
    return;
  }

  try {
    const cartData = {
      userId: currentUser.value.userId,
      goodsId: goodsId.value,
      skuId: currentSku.value?.id || null,
      amount: amount.value,
      specJson: currentSku.value?.specJson || {},
    };

    const resp = await apiAddToCart(cartData);
    if (resp && resp.code === 200) {
      uni.showToast({ title: "已加入购物车", icon: "success" });
    } else {
      uni.showToast({ title: resp?.msg || "添加失败", icon: "none" });
    }
  } catch (error) {
    console.error("添加到购物车失败:", error);
    if (error.message && error.message.includes("库存")) {
      uni.showToast({
        title: error.message,
        icon: "none",
      });
    } else {
      uni.showToast({
        title: "添加失败",
        icon: "none",
      });
    }
  }
}

// 立即购买
async function buyNow() {
  // 检查用户登录状态
  if (!currentUser.value?.userId) {
    uni.showToast({
      title: "请先登录",
      icon: "none",
    });
    return;
  }

  // 检查是否选择了规格
  if (availableSpecs.value.length > 0 && !currentSku.value) {
    uni.showToast({
      title: "请选择商品规格",
      icon: "none",
    });
    return;
  }

  // 验证购买数量
  if (amount.value < 1) {
    uni.showToast({
      title: "购买数量不能少于1",
      icon: "none",
    });
    return;
  }

  if (amount.value > maxStock.value) {
    uni.showToast({
      title: `库存不足，最多可购买${maxStock.value}件`,
      icon: "none",
    });
    return;
  }

  try {
    // 构建规格JSON
    let specJson = {};
    if (currentSku.value && currentSku.value.specDesc) {
      // 解析规格描述
      if (currentSku.value.specDesc.startsWith("{")) {
        specJson = JSON.parse(currentSku.value.specDesc);
      } else {
        currentSku.value.specDesc.split(",").forEach((item) => {
          const [key, value] = item.split(":");
          if (key && value) {
            specJson[key.trim()] = value.trim();
          }
        });
      }
    }

    // 跳转到订单确认页面
    uni.navigateTo({
      url: `/shop/confirm/confirm?goodsId=${goodsId.value}&skuId=${
        currentSku.value ? currentSku.value.id : ""
      }&amount=${amount.value}&specJson=${encodeURIComponent(
        JSON.stringify(specJson)
      )}`,
    });
  } catch (error) {
    console.error("立即购买失败:", error);
    uni.showToast({
      title: "购买失败",
      icon: "none",
    });
  }
}

// 初始化用户信息
const initUserInfo = () => {
  try {
    const userData = uni.getStorageSync("userData");
    if (userData && userData.userId) {
      currentUser.value = userData;
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
  }
};

// 格式化规格描述
function formatSpecDesc(specDesc) {
  if (!specDesc) return "请选择规格";
  try {
    if (specDesc.startsWith("{")) {
      const specObj = JSON.parse(specDesc);
      return Object.entries(specObj)
        .map(([key, value]) => `${key}:${value}`)
        .join(", ");
    } else {
      return specDesc;
    }
  } catch (e) {
    console.warn("格式化规格描述失败:", specDesc);
    return specDesc;
  }
}

// 页面加载
onLoad((options) => {
  initUserInfo();
  if (options.id) {
    goodsId.value = parseInt(options.id);
    fetchGoodsDetail();
  } else {
    uni.showToast({
      title: "商品ID不能为空",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 监听规格选择变化
onMounted(() => {
  // 规格选择初始化已在fetchGoodsDetail中处理
});
</script>

<style scoped lang="scss">
.goods-detail {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 80px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: #fff;
  border-radius: 12px;
  margin: 10px;
}

.goods-swiper {
  width: 100vw;
  height: 340rpx;
  background: #fff;
}

.goods-img {
  width: 100vw;
  height: 340rpx;
  object-fit: cover;
}

.goods-info {
  background: #fff;
  padding: 18px 16px 10px 16px;
  border-radius: 0 0 12px 12px;
  margin-bottom: 10px;
}

.goods-title {
  font-size: 18px;
  font-weight: 500;
  color: #222;
  margin-bottom: 8px;
}

.goods-price-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .price-row {
    display: flex;
    align-items: center;

    .member-price {
      color: #e02e24;
      font-size: 20px;
      font-weight: bold;
      margin-right: 8px;
      transition: all 0.3s ease;
    }

    .member-tag {
      background: #ffeaea;
      color: #e02e24;
      font-size: 12px;
      border-radius: 4px;
      padding: 0 4px;
      margin-right: 8px;
    }
  }

  .origin-price {
    color: #aaa;
    font-size: 14px;
    text-decoration: line-through;
    margin-top: 2px;
    transition: all 0.3s ease;
  }

  .promo-tag {
    background: #ffefe0;
    color: #ff7d00;
    font-size: 12px;
    border-radius: 4px;
    padding: 0 6px;
    margin-top: 6px;
    margin-left: 0;
  }
}

.sku-info {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #007aff;

  .sku-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  .sku-row:last-child {
    margin-bottom: 0;
  }

  .sku-stock {
    color: #e02e24;
    font-size: 13px;
    font-weight: 500;
  }

  .sku-desc {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
  }

  .sku-price {
    color: #333;
    font-size: 13px;
  }
}

.purchase-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;

  .purchase-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .purchase-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .quantity-control {
      display: flex;
      align-items: center;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background: #fff;
      overflow: hidden;
    }

    .quantity-btn {
      width: 32px;
      height: 32px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;

      &.minus {
        border-right: 1px solid #e0e0e0;
      }

      &.plus {
        border-left: 1px solid #e0e0e0;
      }

      &.disabled {
        background: #f8f8f8;

        .btn-text {
          color: #ccc;
        }
      }

      &:not(.disabled):active {
        background: #f0f0f0;
      }
    }

    .btn-text {
      font-size: 16px;
      color: #666;
      font-weight: 500;
      line-height: 1;
    }

    .quantity-input {
      width: 50px;
      height: 32px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .input-field {
      width: 100%;
      height: 100%;
      border: none;
      background: transparent;
      text-align: center;
      font-size: 13px;
      color: #333;
      box-sizing: border-box;

      &:focus {
        outline: none;
      }

      &::-webkit-inner-spin-button,
      &::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type="number"] {
        -moz-appearance: textfield;
      }
    }
  }
}

.sku-tip {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #007aff;

  .sku-row {
    display: flex;
    align-items: center;
  }

  .tip-text {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
  }
}

.goods-attrs {
  background: #fff;
  margin-top: 10px;
  padding: 14px 16px;
  border-radius: 12px;

  .attr-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .attr-name {
      color: #666;
      font-size: 15px;
      margin-right: 8px;
    }

    .attr-values {
      display: flex;
      flex-wrap: wrap;

      .attr-value {
        background: #f2f2f2;
        color: #333;
        border-radius: 16px;
        padding: 4px 16px;
        margin-right: 8px;
        margin-bottom: 4px;
        font-size: 14px;
        border: 1px solid transparent;
        transition: all 0.2s;
      }

      .attr-value.active {
        background: #ffeaea;
        color: #e02e24;
        border: 1px solid #e02e24;
      }
    }
  }
}

.goods-actions {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2px 8px #eee;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 10px 0;
  z-index: 10;

  .cart-btn {
    background: #fff3e0;
    color: #ff7d00;
    border: 1px solid #ff7d00;
    border-radius: 24px;
    padding: 0 28px;
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    margin-right: 12px;
  }

  .buy-btn {
    background: linear-gradient(90deg, #ff6034, #ee0a24);
    color: #fff;
    border: none;
    border-radius: 24px;
    padding: 0 28px;
    height: 40px;
    font-size: 16px;
    font-weight: 500;
  }
}

.goods-desc {
  background: #fff;
  margin-top: 10px;
  padding: 18px 16px;
  border-radius: 12px;

  .desc-title {
    font-size: 20px;
    font-weight: 600;
    color: #222;
    margin-bottom: 10px;
    letter-spacing: 1px;
  }

  .desc-subtitle {
    font-size: 15px;
    color: #666;
    margin-bottom: 8px;
  }

  .desc-text {
    font-size: 14px;
    color: #444;
    margin-bottom: 12px;
    line-height: 1.7;
  }

  .desc-section {
    margin-bottom: 18px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }

    .section-title {
      font-size: 15px;
      font-weight: 500;
      color: #333;
      margin-bottom: 6px;
      margin-top: 8px;
    }

    ul {
      padding-left: 18px;
      margin: 0;
    }

    li {
      font-size: 14px;
      color: #555;
      margin-bottom: 4px;
      list-style: disc;
    }

    .spec-key {
      color: #888;
      font-size: 14px;
      margin-right: 4px;
    }

    .spec-val {
      color: #333;
      font-size: 14px;
    }

    image {
      display: block;
      border-radius: 8px;
      margin-bottom: 8px;
      box-shadow: 0 2px 8px #f5f5f5;
    }
  }
}

.goods-main {
  display: block;
}

@media (min-width: 900px) {
  .goods-detail {
    max-width: 1100px;
    margin: 32px auto 0 auto;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 32px #e0e0e0;
    padding: 32px 32px 0 32px;
  }

  .goods-main {
    display: flex;
    flex-direction: row;
    gap: 40px;
  }

  .goods-left {
    width: 420px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .goods-swiper {
    width: 420px;
    height: 420px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px #f0f0f0;
    background: #fff;
  }

  .goods-img {
    width: 420px;
    height: 420px;
    object-fit: cover;
  }

  .goods-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .goods-info {
    padding: 0;
    margin-bottom: 18px;
  }

  .goods-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .goods-price-box .member-price {
    font-size: 32px;
  }

  .goods-attrs {
    background: none;
    box-shadow: none;
    border-radius: 0;
    padding: 0;
    margin: 0 0 18px 0;
  }

  .purchase-section {
    .purchase-row {
      .quantity-control {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
      }
    }
  }

  .goods-actions {
    position: static;
    width: 100%;
    box-shadow: none;
    padding: 32px 0 0 0;
    background: none;
    justify-content: flex-start;
    display: flex;
    gap: 24px;
  }

  .goods-actions .cart-btn,
  .goods-actions .buy-btn {
    height: 48px;
    font-size: 20px;
    padding: 0 48px;
    margin-right: 0;
  }

  .goods-desc {
    margin-top: 48px;
    padding: 32px 0;
    background: #fff;
    border-radius: 12px;
  }
}
</style>
