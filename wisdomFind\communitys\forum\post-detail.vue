<template>
  <view class="post-detail-container">
    <!-- 内容区域 -->
    <scroll-view
      ref="contentScroll"
      scroll-y
      class="content-scroll"
      @scrolltolower="onScrollToLower"
    >
      <view v-if="postDetail" class="detail-content">
        <!-- 帖子内容 -->
        <view class="post-section">
          <!-- 帖子标题 -->
          <view class="post-title">{{ postDetail.title }}</view>

          <!-- 作者信息 -->
          <view class="author-section">
            <view class="author-info">
              <LazyImageV2
                :src="postDetail.avatar || '/static/images/placeholder.png'"
                class="author-avatar"
                width="80rpx"
                height="80rpx"
                :border-radius="40"
                mode="aspectFill"
                :enable-preview="true"
              />
              <view class="author-text">
                <view class="author-name">{{ postDetail.nickName }}</view>
                <view class="post-meta">
                  <text class="board-name">{{ postDetail.boardName }}</text>
                  <text class="separator">•</text>
                  <text class="publish-time">{{
                    formatTime(postDetail.createTime)
                  }}</text>
                </view>
              </view>
            </view>
            <view class="author-actions">
              <button v-if="!isOwner" class="follow-btn" @click="toggleFollow">
                {{ isFollowing ? "已关注" : "关注" }}
              </button>
              <view class="more-btn" @click="showMore">
                <uni-icons
                  type="more-filled"
                  size="20"
                  color="#666"
                ></uni-icons>
              </view>
            </view>
          </view>

          <!-- 帖子内容 -->
          <view class="post-content">{{ postDetail.content }}</view>

          <!-- 帖子图片 -->
          <view
            v-if="postDetail.images && postDetail.images.length > 0"
            class="post-images"
          >
            <view
              v-for="(image, index) in postDetail.images"
              :key="index"
              class="image-item"
            >
              <LazyImage
                :src="image"
                mode="aspectFill"
                class="image"
                width="100%"
                height="auto"
                :border-radius="8"
                :enable-preview="true"
                :preview-urls="postDetail.images"
              />
            </view>
          </view>

          <!-- 帖子统计 -->
          <view class="post-stats">
            <view class="stat-item" @click="toggleLike">
              <uni-icons
                :type="postDetail.isLiked ? 'heart-filled' : 'heart'"
                :color="postDetail.isLiked ? '#ff4757' : '#999'"
                size="20"
              ></uni-icons>
              <text :class="{ liked: postDetail.isLiked }">{{
                formatNumber(postDetail.likeCount || 0)
              }}</text>
            </view>
            <view class="stat-item">
              <uni-icons type="chat" color="#999" size="20"></uni-icons>
              <text>{{ formatNumber(postDetail.replyCount || 0) }}</text>
            </view>
            <view class="stat-item">
              <uni-icons type="eye" color="#999" size="20"></uni-icons>
              <text>{{ formatNumber(postDetail.viewCount || 0) }}</text>
            </view>
          </view>
        </view>

        <!-- 楼中楼回复列表 -->
        <view class="replies-section">
          <NestedComment
            ref="nestedComment"
            :postId="postId"
            :commentList="nestedReplyList"
            :scrollToCommentId="scrollToCommentId"
            @submit-comment="handleSubmitComment"
            @submit-reply="handleSubmitReply"
            @like-comment="handleLikeComment"
            @load-more-replies="handleLoadMoreReplies"
            @load-sub-comments="handleLoadSubComments"
          />
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-else class="loading-state">
        <uni-load-more status="loading"></uni-load-more>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {
  getForumPostDetail,
  getForumPostDetailWithView,
  getForumPostReplies,
  createForumPostReply,
  deleteForumPost,
  toggleForumPostLike,
  checkForumPostLike,
} from "@/api/community.js";
import {
  getNestedForumReplies,
  createTopLevelForumReply,
  createReplyForumReply,
  toggleForumReplyLike,
  getMoreForumReplies,
  getSubComments,
  findCommentPath,
} from "@/api/forum_nested_comments.js";
import { formatTime, formatNumber } from "@/utils/timeUtils.js";
import NestedComment from "@/components/NestedComment.vue";
// import LazyImageV2 from "@/components/LazyImageV2.vue";
import {
  connectWebSocket,
  subscribeToCommentNotifications,
  subscribeToContentCommentUpdates,
  unsubscribeFromContentCommentUpdates,
  registerMessageHandler,
  removeMessageHandler,
  isConnected,
} from "@/utils/websocketManager.js";

export default {
  name: "PostDetail",
  components: {
    NestedComment,
    // LazyImageV2,
  },
  data() {
    return {
      postId: "",
      anchorCommentId: "", // 锚点评论ID
      postDetail: null,
      replyList: [], // 保留原有回复列表（兼容）
      nestedReplyList: [], // 新的楼中楼回复列表
      replyContent: "",
      pageNum: 1,
      pageSize: 20,
      hasMore: true,
      loading: false,
      isFollowing: false,
      sortType: "time", // time: 按时间, hot: 按热度
      scrollToCommentId: null, // 需要滚动到的评论ID
    };
  },

  computed: {
    // 判断是否为帖子作者
    isOwner() {
      const userData = uni.getStorageSync("userData");
      return (
        userData &&
        userData.userId &&
        this.postDetail &&
        userData.userId === this.postDetail.userId
      );
    },
  },

  onLoad(options) {
    if (options.postId) {
      this.postId = options.postId;

      // 处理智能跳转的评论ID参数
      if (options.commentId) {
        this.scrollToCommentId = parseInt(options.commentId);
        console.log("🎯 [智能跳转] 需要滚动到评论ID:", this.scrollToCommentId);
      } else if (options.anchorCommentId) {
        // 兼容旧的锚点评论ID
        this.anchorCommentId = options.anchorCommentId;
        this.scrollToCommentId = parseInt(options.anchorCommentId);
        console.log("需要滚动到评论ID:", this.scrollToCommentId);
      }

      this.loadPostDetail();
      this.loadNestedReplies(); // 使用楼中楼回复加载方法
    } else {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }

    // 监听帖子详情刷新事件
    uni.$on("refreshForumPostDetail", (postId) => {
      if (postId === this.postId) {
        this.loadPostDetail();
        this.loadNestedReplies();
      }
    });

    // 初始化实时评论通知
    this.initCommentNotification();
  },

  onUnload() {
    // 移除事件监听
    uni.$off("refreshForumPostDetail");

    // 清理实时评论通知
    this.cleanupCommentNotification();
  },

  methods: {
    // 加载帖子详情
    async loadPostDetail() {
      try {
        const userData = uni.getStorageSync("userData");
        let response;

        // 如果有用户信息，使用记录浏览量的接口
        if (userData && userData.userId) {
          response = await getForumPostDetailWithView(this.postId);
          // 同时检查点赞状态
          this.checkUserActions();
        } else {
          response = await getForumPostDetail(this.postId);
        }

        // 兼容不同的成功码
        if (response.code === 200 || response.code === 0) {
          // 数据字段映射，适配后端返回的字段名
          const data = response.data;
          this.postDetail = {
            ...data,
            title: data.postTitle || data.title,
            content: data.postContent || data.content,
            likeCount: data.likeCount || 0,
            replyCount: data.replyCount || 0,
            viewCount: data.viewCount || 0,
            isLiked: data.isLiked || false,
          };
          console.log("帖子详情加载成功:", this.postDetail);
          console.log("浏览数量:", this.postDetail.viewCount);
          console.log("点赞状态:", this.postDetail.isLiked);
        } else {
          console.error("帖子详情加载失败:", response);
          uni.showToast({
            title: response.msg || "加载失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("加载帖子详情失败:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      }
    },

    // 检查用户操作状态（点赞等）
    async checkUserActions() {
      const userData = uni.getStorageSync("userData");
      if (userData && userData.userId) {
        try {
          // 检查点赞状态
          const likeResponse = await checkForumPostLike(this.postId);
          if (likeResponse.code === 200 && this.postDetail) {
            this.postDetail.isLiked = likeResponse.data.isLiked || false;
            // 同时更新点赞数量
            this.postDetail.likeCount = likeResponse.data.likeCount || 0;
          }
        } catch (error) {
          console.error("检查点赞状态失败:", error);
        }
      }
    },

    // 加载回复列表
    async loadReplies() {
      if (this.loading) return;

      try {
        this.loading = true;

        const response = await getForumPostReplies(
          this.postId,
          this.pageNum,
          this.pageSize,
          this.sortType
        );

        // 兼容不同的成功码
        if (response.code === 200 || response.code === 0) {
          const newData = response.rows || [];

          if (this.pageNum === 1) {
            this.replyList = newData;
          } else {
            this.replyList = this.replyList.concat(newData);
          }

          // 判断是否还有更多数据
          this.hasMore = newData.length >= this.pageSize;
        } else {
          uni.showToast({
            title: response.msg || "加载失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("加载回复列表失败:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },

    // 提交回复
    async submitReply() {
      if (!this.replyContent.trim()) {
        uni.showToast({
          title: "请输入回复内容",
          icon: "none",
        });
        return;
      }

      try {
        const response = await createForumPostReply({
          postId: this.postId,
          content: this.replyContent.trim(),
        });

        // 兼容不同的成功码
        if (response.code === 200 || response.code === 0) {
          uni.showToast({
            title: "回复成功",
            icon: "success",
          });

          // 通知帖子列表刷新，更新回复数统计
          uni.$emit("refreshPostList");

          this.replyContent = "";
          this.pageNum = 1;
          this.loadReplies(); // 重新加载回复列表
          this.loadPostDetail(); // 重新加载帖子详情以更新回复数
        } else {
          uni.showToast({
            title: response.msg || "回复失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("提交回复失败:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      }
    },

    // 切换排序方式
    changeSortType(type) {
      if (this.sortType === type) return;

      this.sortType = type;
      this.pageNum = 1;
      this.loadReplies();
    },

    // 切换帖子点赞
    async toggleLike() {
      if (!this.postDetail) return;

      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        // 调用API切换点赞状态
        const response = await toggleForumPostLike(this.postId);

        if (response.code === 200) {
          // 更新点赞状态
          this.postDetail.isLiked = response.data.isLiked;

          // 更新点赞数量
          this.postDetail.likeCount = response.data.likeCount || 0;

          uni.showToast({
            title: this.postDetail.isLiked ? "点赞成功" : "取消点赞",
            icon: "success",
          });
        } else {
          uni.showToast({
            title: response.msg || "操作失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("点赞操作失败:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      }
    },

    // 切换回复点赞
    toggleReplyLike(reply) {
      reply.isLiked = !reply.isLiked;
      reply.likeCount = (reply.likeCount || 0) + (reply.isLiked ? 1 : -1);
    },

    // 切换关注状态
    toggleFollow() {
      this.isFollowing = !this.isFollowing;
      uni.showToast({
        title: this.isFollowing ? "已关注" : "已取消关注",
        icon: "success",
      });
    },

    // 显示更多操作
    showMore() {
      const itemList = this.isOwner
        ? ["编辑", "删除"] // 作者可以编辑和删除自己的帖子
        : [this.postDetail.isCollected ? "取消收藏" : "收藏", "分享", "举报"]; // 其他用户可以收藏、分享和举报

      uni.showActionSheet({
        itemList,
        success: (res) => {
          const action = itemList[res.tapIndex];
          switch (action) {
            case "编辑":
              this.editPost();
              break;
            case "删除":
              this.deletePost();
              break;
            case "收藏":
            case "取消收藏":
              this.toggleCollect();
              break;
            case "分享":
              this.sharePost();
              break;
            case "举报":
              this.reportPost();
              break;
          }
        },
      });
    },

    // 切换收藏状态
    toggleCollect() {
      if (!this.postDetail) return;

      this.postDetail.isCollected = !this.postDetail.isCollected;
      uni.showToast({
        title: this.postDetail.isCollected ? "收藏成功" : "取消收藏",
        icon: "success",
      });

      // 这里应该调用收藏API
      // await togglePostCollect(this.postId)
    },

    // 分享帖子
    sharePost() {
      if (!this.postDetail) return;

      uni.showActionSheet({
        itemList: ["复制链接", "分享到微信", "分享到朋友圈"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              // 复制链接
              uni.setClipboardData({
                data: `【${this.postDetail.title}】- 来自绿境智慧社区`,
                success: () => {
                  uni.showToast({
                    title: "链接已复制",
                    icon: "success",
                  });
                },
              });
              break;
            case 1:
              // 分享到微信
              uni.share({
                provider: "weixin",
                scene: "WXSceneSession",
                type: 0,
                href: `https://example.com/post/${this.postId}`,
                title: this.postDetail.title,
                summary: this.postDetail.content.substring(0, 100) + "...",
                imageUrl:
                  (this.postDetail.images && this.postDetail.images[0]) || "",
                success: () => {
                  uni.showToast({
                    title: "分享成功",
                    icon: "success",
                  });
                },
                fail: () => {
                  uni.showToast({
                    title: "分享失败",
                    icon: "none",
                  });
                },
              });
              break;
            case 2:
              // 分享到朋友圈
              uni.share({
                provider: "weixin",
                scene: "WXSceneTimeline",
                type: 0,
                href: `https://example.com/post/${this.postId}`,
                title: this.postDetail.title,
                summary: this.postDetail.content.substring(0, 100) + "...",
                imageUrl:
                  (this.postDetail.images && this.postDetail.images[0]) || "",
                success: () => {
                  uni.showToast({
                    title: "分享成功",
                    icon: "success",
                  });
                },
                fail: () => {
                  uni.showToast({
                    title: "分享失败",
                    icon: "none",
                  });
                },
              });
              break;
          }
        },
      });
    },

    // 举报帖子
    reportPost() {
      if (!this.postDetail) return;

      uni.showActionSheet({
        itemList: [
          "垃圾营销",
          "违法违规",
          "色情低俗",
          "涉嫌诈骗",
          "内容侵权",
          "人身攻击",
          "其他",
        ],
        success: (res) => {
          const reasons = [
            "垃圾营销",
            "违法违规",
            "色情低俗",
            "涉嫌诈骗",
            "内容侵权",
            "人身攻击",
            "其他",
          ];
          const reason = reasons[res.tapIndex];

          uni.showModal({
            title: "确认举报",
            content: `确定要举报此帖子为"${reason}"吗？`,
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 这里应该调用举报API
                // await reportPost(this.postId, reason)

                uni.showToast({
                  title: "举报成功，我们会尽快处理",
                  icon: "success",
                  duration: 2000,
                });
              }
            },
          });
        },
      });
    },

    // 回复帖子
    replyToPost(reply) {
      this.replyContent = `回复 @${reply.nickName}：`;
      // 聚焦到输入框
      // uni.pageScrollTo({
      //   scrollTop: 9999,
      //   duration: 300
      // })
    },

    // 编辑帖子
    editPost() {
      uni.navigateTo({
        url: `/communitys/forum/edit?id=${this.postId}`,
      });
    },

    // 删除帖子
    async deletePost() {
      uni.showModal({
        title: "确认删除",
        content: "确定要删除这个帖子吗？删除后无法恢复。",
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: "删除中...",
              });

              const userData = uni.getStorageSync("userData");

              // 调用删除API
              const response = await deleteForumPost(
                this.postId,
                userData.userId
              );

              if (response.code === 200) {
                uni.hideLoading();
                uni.showToast({
                  title: "删除成功",
                  icon: "success",
                });

                // 通知相关页面刷新数据
                uni.$emit("refreshPostList");
                uni.$emit("refreshCommunity");

                // 跳转到帖子列表页面
                setTimeout(() => {
                  const boardId = this.postDetail.boardId;
                  const boardName = this.postDetail.boardName;

                  if (boardId && boardName) {
                    uni.redirectTo({
                      url: `/communitys/forum/post-list?boardId=${boardId}&boardName=${encodeURIComponent(
                        boardName
                      )}`,
                    });
                  } else {
                    // 如果没有版块信息，跳转到版块列表
                    uni.redirectTo({
                      url: "/communitys/forum/board-list",
                    });
                  }
                }, 1000);
              } else {
                uni.hideLoading();
                uni.showToast({
                  title: response.msg || "删除失败",
                  icon: "none",
                });
              }
            } catch (error) {
              console.error("删除帖子失败:", error);
              uni.hideLoading();
              uni.showToast({
                title: "删除失败",
                icon: "none",
              });
            }
          }
        },
      });
    },

    // 预览图片
    previewImage(current, urls) {
      uni.previewImage({
        current,
        urls,
      });
    },

    // 格式化时间 - 使用导入的工具函数
    formatTime,

    // 格式化数字 - 使用导入的工具函数
    formatNumber,

    // 页面滚动到底部
    onScrollToLower() {
      if (this.hasMore && !this.loading) {
        this.pageNum++;
        this.loadReplies();
      }
    },

    // ==================== 楼中楼回复相关方法 ====================

    // 将后端返回的嵌套结构转换为前端期望的扁平化结构
    convertNestedToFlat(nestedData) {
      const flatList = [];

      for (const topReply of nestedData) {
        // 添加顶级回复
        const flatTopReply = {
          ...topReply,
          type: 1, // 确保顶级回复的type为1
          parentId: null, // 确保顶级回复的parentId为null
        };
        flatList.push(flatTopReply);

        // 处理二层和三层回复
        if (topReply.replies && topReply.replies.length > 0) {
          for (const level2Reply of topReply.replies) {
            // 添加二层回复
            const flatLevel2Reply = {
              ...level2Reply,
              type: 2, // 确保二层回复的type为2
              parentId: topReply.replyId || topReply.commentId, // 确保parentId指向顶级回复
            };
            flatList.push(flatLevel2Reply);

            // 处理三层回复
            if (level2Reply.replies && level2Reply.replies.length > 0) {
              for (const level3Reply of level2Reply.replies) {
                // 添加三层回复
                const flatLevel3Reply = {
                  ...level3Reply,
                  type: 3, // 确保三层回复的type为3
                  parentId: level2Reply.replyId || level2Reply.commentId, // 确保parentId指向二层回复
                };
                flatList.push(flatLevel3Reply);
              }
            }
          }
        }
      }

      console.log(
        `🔍 [数据转换] 转换完成: ${nestedData.length}个顶级回复 -> ${flatList.length}条扁平化记录`
      );
      return flatList;
    },

    // 加载楼中楼回复
    async loadNestedReplies() {
      if (!this.postId) return;

      try {
        this.loading = true;
        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        const response = await getNestedForumReplies(
          this.postId,
          currentUserId
        );

        if (response.code === 200 || response.code === 0) {
          const nestedData = response.data || [];
          console.log("🔍 [数据转换] 后端返回的嵌套数据:", nestedData);

          // 将后端返回的嵌套结构转换为前端期望的扁平化结构
          this.nestedReplyList = this.convertNestedToFlat(nestedData);
          console.log(
            "🔍 [数据转换] 转换后的扁平化数据:",
            this.nestedReplyList
          );

          // 确保所有评论的isLiked状态不为null（现在后端已修复，这里保留作为兜底）
          this.nestedReplyList.forEach((comment) => {
            if (comment.isLiked === null || comment.isLiked === undefined) {
              comment.isLiked = false;
            }

            if (comment.replies) {
              comment.replies.forEach((reply) => {
                if (reply.isLiked === null || reply.isLiked === undefined) {
                  reply.isLiked = false;
                }
              });
            }
          });

          // 如果有锚点评论ID，自动展开所有折叠的评论并滚动到目标评论
          if (this.scrollToCommentId) {
            console.log("🎯 [智能跳转] 需要定位评论，自动展开所有折叠");

            // 使用 $nextTick 确保数据更新后再展开
            this.$nextTick(() => {
              this.ensureAllCommentsExpanded();

              // 再次使用 $nextTick 确保展开完成后再滚动
              this.$nextTick(() => {
                this.scrollToTargetComment();
              });
            });
          }
        } else {
          console.error("加载回复失败:", response.msg);
        }
      } catch (error) {
        console.error("加载回复异常:", error);
      } finally {
        this.loading = false;
      }
    },

    // 处理提交主回复
    async handleSubmitComment(data) {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        const response = await createTopLevelForumReply(
          this.postId,
          userData.userId,
          data.content
        );

        if (response.code === 200 || response.code === 0) {
          uni.showToast({
            title: "回复成功",
            icon: "success",
          });

          // 智能更新：添加新的顶级评论到列表开头
          if (response.data) {
            this.nestedReplyList.unshift(response.data);
            console.log("🔄 [智能更新] 成功添加顶级评论");
          } else {
            // 如果没有返回数据，降级为重新加载
            this.loadNestedReplies();
          }
          // 重新加载详情（更新回复数量）
          this.loadPostDetail();
        } else {
          uni.showToast({
            title: response.msg || "回复失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("提交回复异常:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 处理提交楼中楼回复
    async handleSubmitReply(data) {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        const replyData = {
          postId: this.postId,
          parentId: data.parentId,
          rootId: data.rootId,
          type: data.type,
          userId: userData.userId,
          replyToUserId: data.replyToUserId,
          replyToUserName: data.replyToUserName,
          originalCommentId: data.originalCommentId,
          content: data.content,
        };

        const response = await createReplyForumReply(replyData);

        if (response.code === 200 || response.code === 0) {
          uni.showToast({
            title: "回复成功",
            icon: "success",
          });

          // 智能更新：不重新加载整个列表，而是添加新回复到对应位置
          this.addNewReplyToList(response.data, data);
          // 确保用户能看到新回复
          this.ensureNewReplyVisible(response.data, data);
          // 重新加载详情（更新回复数量）
          this.loadPostDetail();
        } else {
          uni.showToast({
            title: response.msg || "回复失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("提交回复异常:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 处理点赞回复 - 完全照搬图文分享的逻辑
    async handleLikeComment(data) {
      try {
        const response = await toggleForumReplyLike(
          data.commentId,
          data.userId
        );

        if (response.code === 200) {
          // 【适配论坛API】: 论坛API数据在response.data中，图文分享直接在response中
          const responseData = response.data || {};
          const isLiked = responseData.isLiked;
          const likeCount = responseData.likeCount;

          // 更新本地数据
          const comment = data.comment;
          comment.isLiked = isLiked;
          comment.likeCount = likeCount;

          // 【关键修复】: 论坛用replyId，图文分享用commentId
          const topReply = this.nestedReplyList.find(
            (r) => r.replyId === data.commentId
          );
          if (topReply) {
            topReply.isLiked = isLiked;
            topReply.likeCount = likeCount;
          } else {
            // 如果是楼中楼回复，在replies中查找并更新
            for (let topReply of this.nestedReplyList) {
              if (topReply.replies) {
                const nestedReply = topReply.replies.find(
                  (r) => r.replyId === data.commentId
                );
                if (nestedReply) {
                  nestedReply.isLiked = isLiked;
                  nestedReply.likeCount = likeCount;
                  break;
                }
              }
            }
          }

          uni.showToast({
            title: isLiked ? "点赞成功" : "取消点赞",
            icon: "success",
          });
        } else {
          uni.showToast({
            title: response.msg || "操作失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("点赞回复异常:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 处理加载更多回复
    async handleLoadMoreReplies(parentId) {
      try {
        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        const response = await getMoreForumReplies(parentId, currentUserId);

        if (response.code === 200 || response.code === 0) {
          // 找到对应的顶级回复，更新其回复列表
          const topReply = this.nestedReplyList.find(
            (reply) => reply.replyId === parentId
          );
          if (topReply) {
            topReply.replies = response.data || [];
          }
        }
      } catch (error) {
        console.error("加载更多回复异常:", error);
        uni.showToast({
          title: "加载失败",
          icon: "none",
        });
      }
    },

    // 处理按需加载子评论
    async handleLoadSubComments(parentId) {
      try {
        console.log("🔄 [按需加载] 开始加载子评论:", parentId);

        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        const response = await getSubComments(parentId, currentUserId);

        if (response.code === 200 || response.code === 0) {
          // 找到对应的顶级评论，更新其回复列表
          const topComment = this.nestedReplyList.find(
            (comment) => (comment.commentId || comment.replyId) === parentId
          );
          if (topComment) {
            topComment.replies = response.data || [];
            console.log(
              "🔄 [按需加载] 子评论加载成功:",
              response.data?.length || 0,
              "条"
            );
          } else {
            console.warn("🔄 [按需加载] 未找到父评论:", parentId);
          }
        } else {
          console.error("🔄 [按需加载] 加载失败:", response.msg);
        }
      } catch (error) {
        console.error("🔄 [按需加载] 加载子评论异常:", error);
        uni.showToast({
          title: "加载失败",
          icon: "none",
        });
      }
    },

    // 智能添加新回复到列表（保持展开状态）
    addNewReplyToList(newReply, originalData) {
      if (!newReply || !originalData) {
        console.warn("🔄 [智能更新] 缺少必要数据，降级为重新加载");
        this.loadNestedReplies();
        return;
      }

      console.log("🔄 [智能更新] 添加新回复到列表:", newReply);
      console.log("🔄 [智能更新] 原始数据:", originalData);

      try {
        // 根据回复类型处理
        if (originalData.type === 2) {
          // 回复顶级评论：添加到对应顶级评论的replies数组
          const parentComment = this.nestedReplyList.find(
            (comment) =>
              (comment.commentId || comment.replyId) === originalData.parentId
          );

          if (parentComment) {
            if (!parentComment.replies) {
              parentComment.replies = [];
            }
            parentComment.replies.push(newReply);
            parentComment.replyCount = (parentComment.replyCount || 0) + 1;
            console.log("🔄 [智能更新] 成功添加二级回复");
          } else {
            console.warn("🔄 [智能更新] 未找到父评论，降级为重新加载");
            this.loadNestedReplies();
          }
        } else if (originalData.type === 3) {
          // 回复子评论：添加到对应顶级评论的replies数组
          const rootComment = this.nestedReplyList.find(
            (comment) =>
              (comment.commentId || comment.replyId) === originalData.rootId
          );

          if (rootComment) {
            if (!rootComment.replies) {
              rootComment.replies = [];
            }
            rootComment.replies.push(newReply);
            rootComment.replyCount = (rootComment.replyCount || 0) + 1;
            console.log("🔄 [智能更新] 成功添加三级回复");
          } else {
            console.warn("🔄 [智能更新] 未找到根评论，降级为重新加载");
            this.loadNestedReplies();
          }
        } else {
          console.warn("🔄 [智能更新] 未知回复类型，降级为重新加载");
          this.loadNestedReplies();
        }
      } catch (error) {
        console.error("🔄 [智能更新] 更新失败，降级为重新加载:", error);
        this.loadNestedReplies();
      }
    },

    // 确保新回复对用户可见
    ensureNewReplyVisible(newReply, originalData) {
      if (!newReply || !originalData) return;

      console.log("🎯 [可见性确保] 开始确保新回复可见:", newReply);

      // 等待DOM更新
      this.$nextTick(() => {
        setTimeout(() => {
          const newReplyId = newReply.replyId || newReply.commentId;
          console.log("🎯 [可见性确保] 新回复ID:", newReplyId);

          // 检查新回复是否在当前可见区域
          if (this.isReplyCurrentlyVisible(newReplyId, originalData)) {
            console.log("🎯 [可见性确保] 新回复已在可见区域，直接定位");
            this.scrollToNewReply(newReplyId);
          } else {
            console.log("🎯 [可见性确保] 新回复不在可见区域，需要展开");
            this.expandAndScrollToNewReply(newReplyId, originalData);
          }
        }, 500); // 增加等待时间，确保DOM更新完成
      });
    },

    // 检查回复是否在当前可见区域
    isReplyCurrentlyVisible(replyId, originalData) {
      console.log("🔍 [可见性检测] 开始检查回复可见性:", replyId, originalData);

      // 检查父评论是否在显示的评论列表中
      const parentCommentId =
        originalData.type === 2 ? originalData.parentId : originalData.rootId;

      console.log("🔍 [可见性检测] 父评论ID:", parentCommentId);

      // 如果是回复顶级评论，检查顶级评论是否可见
      if (originalData.type === 2) {
        // 检查顶级评论是否在当前显示的列表中
        const topCommentIndex = this.nestedReplyList.findIndex(
          (comment) =>
            (comment.commentId || comment.replyId) === parentCommentId
        );

        console.log("🔍 [可见性检测] 父评论在列表中的索引:", topCommentIndex);
        console.log(
          "🔍 [可见性检测] 评论列表总数:",
          this.nestedReplyList.length
        );

        if (topCommentIndex === -1) {
          console.log("🔍 [可见性检测] 父评论不在列表中，不可见");
          return false;
        }

        // 检查是否在折叠区域（前5条之外）
        if (this.nestedReplyList.length > 5 && topCommentIndex >= 5) {
          // 检查顶级评论是否展开
          const nestedComment = this.$refs.nestedComment;
          console.log(
            "🔍 [可见性检测] 顶级评论展开状态:",
            nestedComment?.topCommentsExpanded
          );
          if (nestedComment && !nestedComment.topCommentsExpanded) {
            console.log("🔍 [可见性检测] 父评论在折叠区域且未展开，不可见");
            return false; // 在折叠区域且未展开
          }
        }

        // 检查父评论的回复是否展开
        const parentComment = this.nestedReplyList[topCommentIndex];
        console.log(
          "🔍 [可见性检测] 父评论回复数量:",
          parentComment.replies?.length || 0
        );
        console.log(
          "🔍 [可见性检测] 父评论replyCount:",
          parentComment.replyCount || 0
        );

        // 使用replyCount而不是replies.length，因为replies可能被截断显示
        const totalReplies =
          parentComment.replyCount || parentComment.replies?.length || 0;
        console.log("🔍 [可见性检测] 实际回复总数:", totalReplies);

        if (totalReplies > 3) {
          const nestedComment = this.$refs.nestedComment;
          const isExpanded = nestedComment?.foldedComments.has(parentCommentId);
          console.log("🔍 [可见性检测] 父评论回复展开状态:", isExpanded);
          console.log(
            "🔍 [可见性检测] foldedComments集合:",
            Array.from(nestedComment?.foldedComments || [])
          );
          if (nestedComment && !isExpanded) {
            console.log("🔍 [可见性检测] 父评论回复区域未展开，不可见");
            return false; // 回复区域未展开（不在集合中表示折叠）
          }
        }
      }

      console.log("🔍 [可见性检测] 回复可见");
      return true; // 默认认为可见
    },

    // 展开并滚动到新回复
    expandAndScrollToNewReply(replyId, originalData) {
      console.log("🎯 [自动展开] 开始展开并定位到新回复:", replyId);

      const parentCommentId =
        originalData.type === 2 ? originalData.parentId : originalData.rootId;
      const parentCommentIndex = this.nestedReplyList.findIndex(
        (comment) => (comment.commentId || comment.replyId) === parentCommentId
      );

      if (parentCommentIndex === -1) {
        console.warn("🎯 [自动展开] 未找到父评论，无法展开");
        return;
      }

      const nestedComment = this.$refs.nestedComment;
      if (!nestedComment) {
        console.warn("🎯 [自动展开] 未找到NestedComment组件");
        return;
      }

      // 如果父评论在折叠区域，先展开顶级评论
      if (
        this.nestedReplyList.length > 5 &&
        parentCommentIndex >= 5 &&
        !nestedComment.topCommentsExpanded
      ) {
        console.log("🎯 [自动展开] 展开顶级评论区域");
        nestedComment.toggleTopCommentsFold();
      }

      // 展开父评论的回复区域（如果还没展开的话）
      if (!nestedComment.foldedComments.has(parentCommentId)) {
        console.log("🎯 [自动展开] 展开父评论的回复区域:", parentCommentId);
        nestedComment.foldedComments.add(parentCommentId);
        // 强制更新视图
        nestedComment.$forceUpdate();
      }

      // 等待展开完成后滚动
      this.$nextTick(() => {
        setTimeout(() => {
          this.scrollToNewReply(replyId);
        }, 500);
      });
    },

    // 滚动到新回复 - 修复版
    scrollToNewReply(replyId) {
      console.log("🎯 [滚动定位] 滚动到新回复:", replyId);

      // 增加多次尝试机制，确保DOM完全更新
      let attempts = 0;
      const maxAttempts = 5;

      const tryScroll = () => {
        attempts++;
        console.log(`🎯 [滚动定位] 第${attempts}次尝试滚动到回复:`, replyId);

        // 尝试多种可能的选择器格式
        const selectors = [
          `#comment-${replyId}`,
          `.comment-${replyId}`,
          `[data-comment-id="${replyId}"]`,
        ];

        // 使用第一个选择器尝试滚动
        const selector = selectors[0];
        console.log("🎯 [滚动定位] 使用选择器:", selector);

        uni.pageScrollTo({
          selector: selector,
          duration: 300,
          success: () => {
            console.log("🎯 [滚动定位] 滚动成功");
            this.highlightNewReply(replyId);
          },
          fail: (error) => {
            console.warn(`🎯 [滚动定位] 第${attempts}次滚动失败:`, error);

            // 如果还有尝试次数，继续尝试
            if (attempts < maxAttempts) {
              setTimeout(tryScroll, 300);
            } else {
              console.error("🎯 [滚动定位] 所有滚动尝试都失败，使用备用方案");
              this.fallbackScrollToReply(replyId);
            }
          },
        });
      };

      // 等待DOM更新后开始尝试
      this.$nextTick(() => {
        setTimeout(tryScroll, 200);
      });
    },

    // 高亮新回复
    highlightNewReply(replyId) {
      console.log("🎯 [高亮显示] 高亮新回复:", replyId);
      const nestedComment = this.$refs.nestedComment;
      if (nestedComment) {
        nestedComment.highlightedCommentId = replyId;
        setTimeout(() => {
          nestedComment.highlightedCommentId = null;
        }, 2000);
      }
    },

    // 备用滚动方案
    fallbackScrollToReply(replyId) {
      console.log("🎯 [备用滚动] 使用备用方案滚动到回复:", replyId);

      // 尝试滚动到评论区域
      uni.pageScrollTo({
        selector: ".replies-section",
        duration: 300,
        success: () => {
          console.log("🎯 [备用滚动] 滚动到评论区域成功");
          this.highlightNewReply(replyId);
        },
        fail: () => {
          // 最后的备用方案：滚动到页面底部
          console.log("🎯 [备用滚动] 滚动到页面底部");
          uni.pageScrollTo({
            scrollTop: 99999,
            duration: 300,
          });
          this.highlightNewReply(replyId);
        },
      });
    },

    // 执行滚动操作
    performScroll(selector, replyId) {
      console.log("🎯 [滚动定位] 执行滚动操作:", selector);

      uni.pageScrollTo({
        selector: selector,
        duration: 500,
        success: () => {
          console.log("🎯 [滚动定位] 滚动成功");
          // 短暂高亮新回复
          const nestedComment = this.$refs.nestedComment;
          if (nestedComment) {
            nestedComment.highlightedCommentId = replyId;
            setTimeout(() => {
              nestedComment.highlightedCommentId = null;
            }, 2000);
          }
        },
        fail: (error) => {
          console.warn("🎯 [滚动定位] 滚动失败:", error);
        },
      });
    },

    // ========== WebSocket实时通知相关方法 ==========

    /**
     * 初始化评论通知
     */
    async initCommentNotification() {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        return;
      }

      try {
        // 连接WebSocket (如果还没连接)
        if (!isConnected()) {
          await connectWebSocket(
            userData.userId.toString(),
            userData.token || ""
          );
        }

        // 个人评论通知现在由全局统一处理，这里只处理评论更新
        // 注释掉页面级的评论通知处理器，避免重复显示
        // registerMessageHandler('commentNotification', (message) => {
        //   console.log('🎉 [PostDetail] 收到个人评论通知，自动刷新评论列表')
        //
        //   // 只自动刷新评论列表，不显示通知（通知由全局处理）
        //   this.loadNestedReplies()
        // })

        // 注册评论更新处理器
        registerMessageHandler("commentUpdate", (data) => {
          console.log("🔥 [PostDetail DEBUG] 收到评论更新:", data);
          console.log(
            "🔥 [PostDetail DEBUG] data.contentType:",
            data.contentType
          );
          console.log("🔥 [PostDetail DEBUG] data.contentId:", data.contentId);
          console.log("🔥 [PostDetail DEBUG] this.postId:", this.postId);
          console.log(
            "🔥 [PostDetail DEBUG] 类型匹配:",
            data.contentType === "post"
          );
          console.log(
            "🔥 [PostDetail DEBUG] ID匹配:",
            data.contentId == this.postId
          );

          if (data.contentType === "post" && data.contentId == this.postId) {
            console.log("🔥 [PostDetail DEBUG] 条件匹配，静默刷新评论列表");

            // 静默刷新评论列表（不显示提示）
            this.loadNestedReplies();

            // 更新帖子回复数
            if (this.postDetail) {
              const oldCount = this.postDetail.replyCount || 0;
              this.postDetail.replyCount = oldCount + 1;
              console.log(
                "🔥 [PostDetail DEBUG] 回复数更新:",
                oldCount,
                "->",
                this.postDetail.replyCount
              );
            }

            // 注意：不显示Toast提示，因为这是广播更新
            // 只有个人通知（commentNotification）才显示提示
          } else {
            console.log("🔥 [PostDetail DEBUG] 条件不匹配，忽略消息");
          }
        });

        // 个人评论通知已在App启动时全局订阅，这里不需要重复订阅
        // subscribeToCommentNotifications(userData.userId)

        // 订阅当前帖子的评论更新
        subscribeToContentCommentUpdates("post", this.postId);

        console.log("[PostDetail] WebSocket通知已初始化");
      } catch (error) {
        console.error("[PostDetail] 初始化WebSocket通知失败:", error);
      }
    },

    /**
     * 清理评论通知
     */
    cleanupCommentNotification() {
      try {
        // 取消订阅当前帖子的评论更新
        unsubscribeFromContentCommentUpdates("post", this.postId);

        // 移除消息处理器
        removeMessageHandler("commentNotification");
        removeMessageHandler("commentUpdate");

        console.log("[PostDetail] WebSocket通知已清理");
      } catch (error) {
        console.error("[PostDetail] 清理WebSocket通知失败:", error);
      }
    },

    /**
     * 显示评论通知
     */
    showCommentNotification(message) {
      try {
        let title = "新回复";

        switch (message.eventType) {
          case "FORUM_REPLY":
            title = `${message.fromUserName} 回复了您的帖子`;
            break;
          case "NESTED_REPLY":
            title = `${message.fromUserName} 回复了您`;
            break;
          default:
            title = `${message.fromUserName} 回复了您`;
        }

        // 显示系统通知
        uni.showToast({
          title: title,
          icon: "none",
          duration: 3000,
        });

        // 可以在这里添加更丰富的通知效果
        // 比如闪烁提示、声音提醒等
      } catch (error) {
        console.error("[PostDetail] 显示通知失败:", error);
      }
    },

    /**
     * 滚动到目标评论
     */
    scrollToTargetComment() {
      if (!this.scrollToCommentId) return;

      console.log("🎯 [智能跳转] 开始滚动到评论ID:", this.scrollToCommentId);

      // 直接执行滚动，无需延迟
      this.performScrollToComment();
    },

    /**
     * 执行滚动到评论
     */
    performScrollToComment() {
      try {
        console.log(
          "🎯 [智能跳转] 执行滚动，查找元素: #comment-" + this.scrollToCommentId
        );

        // 调用新的智能滚动方法
        this.scrollToCommentById(this.scrollToCommentId);

        // 清除滚动标记
        this.scrollToCommentId = null;
      } catch (error) {
        console.error("🎯 [智能跳转] 滚动到目标评论异常:", error);
        this.scrollToCommentId = null;
      }
    },

    // 滚动到指定评论ID（直接测量实际位置）
    scrollToCommentById(commentId) {
      console.log("🎯 [智能跳转] 直接测量实际位置，评论ID:", commentId);

      // 直接测量评论的实际位置
      const query = uni.createSelectorQuery().in(this.$refs.nestedComment);
      query.select(`#comment-${commentId}`).boundingClientRect();

      // 同时获取scroll-view的信息
      const globalQuery = uni.createSelectorQuery();
      globalQuery.select(".content-scroll").boundingClientRect();
      globalQuery.select(".content-scroll").scrollOffset();

      query.exec((commentRes) => {
        globalQuery.exec((globalRes) => {
          if (commentRes[0] && globalRes[0] && globalRes[1]) {
            const commentRect = commentRes[0];
            const scrollViewRect = globalRes[0];
            const scrollOffset = globalRes[1];

            // 计算评论在scroll-view中的相对位置
            const relativeTop =
              commentRect.top - scrollViewRect.top + scrollOffset.scrollTop;
            const targetScrollTop = relativeTop - 100; // 留100px空间

            console.log(
              "🎯 [智能跳转] 找到评论，实际位置:",
              relativeTop,
              "目标位置:",
              targetScrollTop
            );
            this.scrollTop = targetScrollTop;

            // 高亮显示目标评论
            setTimeout(() => {
              this.highlightTargetComment();
            }, 500);
          } else {
            console.log("🎯 [智能跳转] 未找到评论，滚动到评论区顶部");
            this.scrollToTargetCommentFallback();
          }
        });
      });
    },

    /**
     * 查找评论元素
     */
    findCommentElement(commentId) {
      // 在顶级评论中查找
      const topComment = this.nestedReplyList.find(
        (comment) =>
          comment.commentId === commentId || comment.replyId === commentId
      );

      if (topComment) {
        return true;
      }

      // 在回复中查找
      for (const comment of this.nestedReplyList) {
        if (comment.replies) {
          const reply = comment.replies.find(
            (reply) =>
              reply.commentId === commentId || reply.replyId === commentId
          );
          if (reply) {
            return true;
          }
        }
      }

      return false;
    },

    /**
     * 高亮显示目标评论
     */
    highlightTargetComment() {
      // 在uni-app中，我们通过修改数据来触发高亮效果
      // 为评论添加高亮标记
      this.addHighlightToComment(this.scrollToCommentId);

      // 3秒后移除高亮
      setTimeout(() => {
        this.removeHighlightFromComment(this.scrollToCommentId);
      }, 3000);
    },

    /**
     * 为评论添加高亮标记
     */
    addHighlightToComment(commentId) {
      // 在顶级评论中查找并添加高亮
      const topCommentIndex = this.nestedReplyList.findIndex(
        (comment) =>
          comment.commentId === commentId || comment.replyId === commentId
      );

      if (topCommentIndex >= 0) {
        this.$set(this.nestedReplyList[topCommentIndex], "isHighlighted", true);
        return;
      }

      // 在回复中查找并添加高亮
      for (let i = 0; i < this.nestedReplyList.length; i++) {
        const comment = this.nestedReplyList[i];
        if (comment.replies) {
          const replyIndex = comment.replies.findIndex(
            (reply) =>
              reply.commentId === commentId || reply.replyId === commentId
          );
          if (replyIndex >= 0) {
            this.$set(comment.replies[replyIndex], "isHighlighted", true);
            return;
          }
        }
      }
    },

    /**
     * 移除评论的高亮标记
     */
    removeHighlightFromComment(commentId) {
      // 在顶级评论中查找并移除高亮
      const topCommentIndex = this.nestedReplyList.findIndex(
        (comment) =>
          comment.commentId === commentId || comment.replyId === commentId
      );

      if (topCommentIndex >= 0) {
        this.$set(
          this.nestedReplyList[topCommentIndex],
          "isHighlighted",
          false
        );
        return;
      }

      // 在回复中查找并移除高亮
      for (let i = 0; i < this.nestedReplyList.length; i++) {
        const comment = this.nestedReplyList[i];
        if (comment.replies) {
          const replyIndex = comment.replies.findIndex(
            (reply) =>
              reply.commentId === commentId || reply.replyId === commentId
          );
          if (replyIndex >= 0) {
            this.$set(comment.replies[replyIndex], "isHighlighted", false);
            return;
          }
        }
      }
    },

    /**
     * 确保所有评论都已展开（数据驱动）
     */
    ensureAllCommentsExpanded() {
      console.log("🎯 [智能跳转] 确保所有评论都已展开");

      // 自动点击所有展开按钮
      this.clickAllExpandButtons();
    },

    // 自动点击所有展开按钮
    clickAllExpandButtons() {
      console.log("🎯 [智能跳转] 开始自动点击展开按钮");

      if (!this.$refs.nestedComment) {
        console.log("🎯 [智能跳转] 子组件引用不存在");
        return;
      }

      // 直接调用子组件的展开方法，确保所有评论都展开
      const nestedComment = this.$refs.nestedComment;

      // 1. 展开所有顶级评论
      if (nestedComment.commentList.length > 5) {
        console.log("🎯 [智能跳转] 展开所有顶级评论");
        nestedComment.expandAllTopComments();
      }

      // 2. 展开所有回复
      console.log("🎯 [智能跳转] 展开所有回复");
      nestedComment.expandAllReplies();

      // 3. 展开所有子回复
      console.log("🎯 [智能跳转] 展开所有子回复");
      nestedComment.expandAllSubReplies();

      console.log("🎯 [智能跳转] 所有展开按钮点击完成");
    },

    /**
     * 为了定位目标评论，自动展开所有折叠的评论
     */
    expandAllCommentsForTarget() {
      console.log("🎯 [智能跳转] 开始展开所有折叠的评论");

      // 使用和图文分享一样的展开逻辑
      this.ensureAllCommentsExpanded();
    },

    /**
     * 滚动到目标评论的备用方法
     */
    scrollToTargetCommentFallback() {
      console.log("使用备用滚动方法");

      // 计算目标评论的大概位置
      let targetIndex = -1;
      let currentIndex = 0;

      // 在顶级评论中查找
      for (const comment of this.nestedReplyList) {
        if (
          comment.commentId === this.scrollToCommentId ||
          comment.replyId === this.scrollToCommentId
        ) {
          targetIndex = currentIndex;
          break;
        }
        currentIndex++;

        // 在回复中查找
        if (comment.replies) {
          for (const reply of comment.replies) {
            if (
              reply.commentId === this.scrollToCommentId ||
              reply.replyId === this.scrollToCommentId
            ) {
              targetIndex = currentIndex;
              break;
            }
            currentIndex++;
          }
        }
      }

      if (targetIndex >= 0) {
        // 估算滚动位置（每个评论大约200rpx高度）
        const estimatedPosition = targetIndex * 200;

        // 使用scroll-view的scrollTop属性滚动
        const scrollView = this.$refs.contentScroll;
        if (scrollView) {
          scrollView.scrollTop = estimatedPosition;
        }

        console.log("备用滚动完成，目标索引:", targetIndex);
      }

      // 清除滚动标记
      this.scrollToCommentId = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.post-detail-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-scroll {
  flex: 1;
  height: calc(100vh - 120rpx);
}

.detail-content {
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.post-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
}

.post-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.author-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.author-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.author-text {
  flex: 1;
}

.author-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.board-name {
  color: #007aff;
}

.separator {
  margin: 0 4rpx;
}

.author-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.follow-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;

  &::after {
    border: none;
  }
}

.more-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:active {
    background: #e8e8e8;
  }
}

.post-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.image-item {
  width: calc(33.33% - 7rpx);
  height: 200rpx;
}

.image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.post-stats {
  display: flex;
  gap: 40rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #999;
  transition: color 0.2s;

  &:active {
    color: #007aff;
  }

  .liked {
    color: #ff4757;
  }
}

.replies-section {
  background: #fff;
  border-radius: 16rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.sort-options {
  display: flex;
  gap: 20rpx;
}

.sort-item {
  font-size: 26rpx;
  color: #999;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.2s;

  &.active {
    background: #007aff;
    color: #fff;
  }

  &:active {
    transform: scale(0.95);
  }
}

.reply-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.reply-author-info {
  flex: 1;
}

.reply-author-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
}

.reply-floor {
  font-size: 24rpx;
  color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.reply-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.reply-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.reply-image-item {
  width: 120rpx;
  height: 120rpx;
}

.reply-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.reply-actions {
  display: flex;
  gap: 40rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
  transition: color 0.2s;

  &:active {
    color: #007aff;
  }

  .liked {
    color: #ff4757;
  }
}

.reply-input-section {
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}

.reply-textarea {
  flex: 1;
  min-height: 80rpx;
  max-height: 200rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.4;
  resize: none;
}

.submit-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  min-width: 120rpx;

  &::after {
    border: none;
  }

  &:disabled {
    background: #ccc;
    color: #fff;
  }
}

.loading-state {
  padding: 60rpx;
  text-align: center;
}

/* 高亮目标评论样式 */
.highlight-comment {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
  border: 2rpx solid #ffc107 !important;
  border-radius: 16rpx !important;
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}
</style>
