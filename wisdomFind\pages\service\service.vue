<template>
  <view class="page-container">
    <!-- 顶部图片 -->
    <view class="header-image-container">
      <image class="header-image" src="https://copyright.bdstatic.com/vcg/creative/d2cbbdbbfc064f87c3a63497b9087294.jpg@h_1280" mode="widthFix"></image>
      <view class="header-text">
        <text class="header-main-text">绿境服务</text>
        <text class="header-sub-text">保护您的每一株植物</text>
      </view>
    </view>
    
    <!-- 便捷AI标题 -->
    <view class="section-title">
      <text>便捷AI</text>
    </view>
    
    <!-- AI服务区块 -->
    <view class="ai-section">
      <view class="ai-card" @click="navigateToAiAssistant">
        <view class="ai-content">
          <text class="ai-title">AI助手</text>
          <text class="ai-desc">智能植物养护指导</text>
        </view>
        <image class="ai-icon" src="https://copyright.bdstatic.com/vcg/creative/2637d1a20dab211f46cce038b8daa371911af6dc.jpg" mode="aspectFill"></image>
      </view>
      
      <view class="ai-card" @click="navigateToAiService">
        <view class="ai-content">
          <text class="ai-title">AI客服</text>
          <text class="ai-desc">24小时在线解答</text>
        </view>
        <image class="ai-icon" src="https://copyright.bdstatic.com/vcg/creative/2637d1a20dab211f46cce038b8daa371911af6dc.jpg" mode="aspectFill"></image>
      </view>
    </view>
    
    <!-- 人工客服标题 -->
    <view class="section-title">
      <text>人工客服</text>
    </view>
    
    <!-- 人工客服区块 -->
    <view class="manual-service">
      <view class="service-item" @click="contactOnlineService">
        <view class="service-content">
          <text class="service-title">在线客服</text>
          <text class="service-desc">在线发起咨询</text>
        </view>
        <image class="service-icon" src="https://copyright.bdstatic.com/vcg/creative/2637d1a20dab211f46cce038b8daa371911af6dc.jpg" mode="aspectFill"></image>
      </view>
      
      <view class="service-item" @click="makePhoneCall">
        <view class="service-content">
          <text class="service-title">电话客服</text>
          <text class="service-desc">电话咨询问题</text>
        </view>
        <image class="service-icon" src="https://copyright.bdstatic.com/vcg/creative/2637d1a20dab211f46cce038b8daa371911af6dc.jpg" mode="aspectFill"></image>
      </view>
    </view>
    
    <!-- 其他服务标题 -->
    <view class="section-title">
      <text>其他服务</text>
    </view>
    
    <!-- 其他服务区块 -->
    <view class="other-services">
      <view class="service-grid" v-for="item in otherServices" :key="item.id" @click="handleServiceClick(item)">
        <uni-icons :type="item.iconType" size="30" color="#333" />
        <text class="grid-text">{{item.name}}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

const id = ref('');
const otherServices = ref([
  {id: 1, name: '常见故障', iconType: 'help'},
  {id: 2, name: '电子说明书', iconType: 'list'},
  {id: 3, name: '知识库客服', iconType: 'info'},
   {id: 5, name: '意见反馈', iconType: 'compose'},
 //  {id: 4, name: '种植百科', iconType: 'wallet'}
 
]);

// 获取用户ID的方法
const getUserId = () => {
  try {
    const userData = uni.getStorageSync('userData');
    if (userData && userData.userId) {
      id.value = userData.userId;
      console.log('获取到用户ID:', id.value);
    } else {
      console.error('未找到用户数据');
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('获取用户ID异常:', error);
  }
};

onLoad(() => {
  getUserId();
});

const navigateToAiAssistant = () => {
  uni.navigateTo({
    url: '/service/aiassistant/aiassistant'
  });
};

const navigateToAiService = () => {
  uni.navigateTo({
    url: '/pages/ai/service'
  });
};

const contactOnlineService = () => {
  uni.navigateTo({
    url: '/pages/service/online'
  });
};

const makePhoneCall = () => {
  uni.navigateTo({
    url: '/service/telephoneCustomer/telephoneCustomer'
  });
};

const handleServiceClick = (item) => {
  switch(item.name) {
    case '意见反馈':
    uni.navigateTo({
        url: `/service/feedback/feedback?userId=${id.value}`
      });
      break;
    case '电子说明书':
      uni.navigateTo({
        url: '/service/manual/manual'
      });
      break;
    case '常见故障':
      uni.navigateTo({
        url: '/service/commonfault/commonfault'
      });
      break;
	  case '知识库客服':
	    uni.navigateTo({
	      url: '/service/aiknowledge/aiknowledge'
	    });
	    break;
    default:
      uni.showToast({
        title: `打开${item.name}`,
        icon: 'none'
      });
  }
};
</script>

<style lang="scss">
.page-container {
  padding-bottom: 30rpx;
  background-color: #f7f7f7;
  
  .header-image-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    
    .header-image {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
    
    .header-text {
      position: absolute;
      bottom: 130rpx;
      left: 20rpx;
      text-align: left;
      color: #fff;
      z-index: 1;
      
      .header-main-text {
        font-size: 52rpx;
        font-weight: bold;
      }
      
      .header-sub-text {
        font-size: 28rpx;
        margin-top: 10rpx;
        white-space: pre-line;
      }
    }
  }
  
  .section-title {
    padding: 30rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    background-color: #fff;
    margin-top: 20rpx;
  }
  
  .ai-section {
    display: flex;
    justify-content: space-around;
    padding: 20rpx;
    background-color: #fff;
    
    .ai-card {
      display: flex;
      align-items: center;
      padding: 10rpx;
      background-color: #f9f9f9;
      border-radius: 16rpx;
      width: 45%;
      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
      
      .ai-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;
        padding-left: 20rpx;
        
        .ai-title {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }
        
        .ai-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
      
      .ai-icon {
        width: 100rpx;
        height: 100rpx;
        margin-left: 20rpx;
        clip-path: polygon(0 0, 100% 0, 100% 100%);
      }
    }
  }
  
  .manual-service {
    display: flex;
    justify-content: space-around;
    padding: 20rpx;
    background-color: #fff;
    
    .service-item {
      display: flex;
      align-items: center;
      padding: 10rpx;
      background-color: #f9f9f9;
      border-radius: 16rpx;
      width: 45%;
      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
      
      .service-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;
        padding-left: 20rpx;
        
        .service-title {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }
        
        .service-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
      
      .service-icon {
        width: 100rpx;
        height: 100rpx;
        margin-left: 20rpx;
        clip-path: polygon(0 0, 100% 0, 100% 100%);
      }
    }
  }
  
  .other-services {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    padding: 20rpx 0;
    
    .service-grid {
      width: 33.33%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30rpx 0;
      
      .grid-text {
        font-size: 26rpx;
        margin-top: 10rpx;
      }
    }
  }
}
</style>