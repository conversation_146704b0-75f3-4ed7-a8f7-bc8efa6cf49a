<template>
  <view class="imagetext-list-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <uni-search-bar
        v-model="searchKeyword"
        placeholder="搜索图文分享内容"
        @search="onSearch"
        @input="onSearchInput"
        cancelButton="none"
      ></uni-search-bar>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view
          v-for="tab in filterTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: currentFilter === tab.value }"
          @click="switchFilter(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
    </view>

    <!-- 图文列表 -->
    <view class="imagetext-list">
      <view
        v-for="(item, index) in imagetextList"
        :key="`imagetext_${item.imagetextId}_${index}`"
        class="imagetext-item"
        @click="goToDetail(item.imagetextId)"
      >
        <!-- 图片展示 -->
        <view class="item-images">
          <image
            :src="item.coverImageUrl || '/static/images/placeholder.png'"
            mode="aspectFill"
            class="cover-image"
            @click.stop="previewCoverImage(item)"
          ></image>
        </view>

        <!-- 内容信息 -->
        <view class="item-content">
          <view class="item-title">{{ item.imagetextTitle }}</view>

          <!-- 作者信息 -->
          <view class="author-info">
            <image
              :src="item.avatar || '/static/images/placeholder.png'"
              class="author-avatar"
              @click.stop="previewAvatar(item)"
            ></image>
            <text class="author-name">{{ item.nickName }}</text>
            <text class="publish-time">{{ formatTime(item.createTime) }}</text>
          </view>

          <!-- 统计信息 -->
          <view class="item-stats">
            <view class="stat-item">
              <uni-icons type="eye" size="16" color="#999"></uni-icons>
              <text>{{ formatNumber(item.viewCount) }}</text>
            </view>
            <view class="stat-item">
              <uni-icons type="chat" size="16" color="#999"></uni-icons>
              <text>{{ formatNumber(item.commentCount) }}</text>
            </view>
            <view class="stat-item">
              <uni-icons
                :type="item.isLiked ? 'heart-filled' : 'heart'"
                size="16"
                :color="item.isLiked ? '#ff4757' : '#999'"
              >
              </uni-icons>
              <text :style="{ color: item.isLiked ? '#ff4757' : '#666' }">{{
                formatNumber(item.likeCount)
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="imagetextList.length === 0 && !loading" class="empty-state">
      <uni-icons type="image" size="80" color="#ccc"></uni-icons>
      <text class="empty-text">暂无图文分享内容</text>
      <view class="empty-action">
        <button class="create-btn" @click="goToCreate">发布第一篇分享</button>
      </view>
    </view>

    <!-- 加载更多 -->
    <uni-load-more
      :status="loadStatus"
      @clickLoadMore="loadMore"
    ></uni-load-more>

    <!-- 悬浮添加按钮 -->
    <view class="fab-container">
      <view class="fab-button" @click="goToCreate">
        <uni-icons type="plus" size="30" color="#fff"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getImageTextList,
  getImageTextListWithStatus,
} from "@/api/community.js";
import { formatTime, formatNumber } from "@/utils/timeUtils.js";

export default {
  name: "ImageTextList",
  data() {
    return {
      imagetextList: [],
      searchKeyword: "",
      currentFilter: "latest",
      pageNum: 1,
      pageSize: 10,
      loading: false,
      loadStatus: "more",
      hasMore: true,
      searchTimer: null,
      filterTabs: [
        { label: "最新", value: "latest" },
        { label: "热门", value: "hot" },
        { label: "我的", value: "mine" },
      ],
    };
  },

  onLoad() {
    this.loadData();

    // 监听刷新事件
    uni.$on("refreshImageTextList", () => {
      this.refreshData();
    });
  },

  onUnload() {
    // 移除事件监听
    uni.$off("refreshImageTextList");
  },

  onShow() {
    // 每次显示页面时刷新数据，确保点赞状态同步
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    if (this.hasMore) {
      this.loadMore();
    }
  },

  methods: {
    // 加载数据
    async loadData() {
      if (this.loading) return;

      try {
        this.loading = true;
        this.loadStatus = "loading";

        const params = {
          status: 1,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchKeyword.trim()) {
          params.imagetextTitle = this.searchKeyword.trim();
        }

        // 添加用户筛选
        if (this.currentFilter === "mine") {
          const userData = uni.getStorageSync("userData");
          if (userData && userData.userId) {
            params.userId = userData.userId;
          }
        }

        const response = await getImageTextListWithStatus(params);

        console.log("图文列表API响应:", response);

        if (response.code === 200 || response.code === 0) {
          const newData = response.rows || [];
          console.log("处理后的图文数据:", newData);

          if (this.pageNum === 1) {
            this.imagetextList = newData;
          } else {
            // 去重：基于imagetextId去除重复项
            const existingIds = new Set(
              this.imagetextList.map((item) => item.imagetextId)
            );
            const uniqueNewData = newData.filter(
              (item) => !existingIds.has(item.imagetextId)
            );
            this.imagetextList = this.imagetextList.concat(uniqueNewData);
          }

          // 判断是否还有更多数据
          this.hasMore = newData.length >= this.pageSize;
          this.loadStatus = this.hasMore ? "more" : "noMore";
        } else {
          this.loadStatus = "more";
          console.error("图文列表接口错误:", response);
          uni.showToast({
            title: response.msg || "加载失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("加载图文列表失败:", error);
        this.loadStatus = "more";
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },

    // 刷新数据
    refreshData() {
      this.pageNum = 1;
      this.hasMore = true;
      this.loadData();
    },

    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return;
      this.pageNum++;
      this.loadData();
    },

    // 搜索输入
    onSearchInput() {
      // 防抖处理
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.searchTimer = setTimeout(() => {
        this.refreshData();
      }, 500);
    },

    // 搜索确认
    onSearch() {
      this.refreshData();
    },

    // 切换筛选
    switchFilter(filter) {
      if (this.currentFilter === filter) return;

      this.currentFilter = filter;
      this.refreshData();
    },

    // 格式化数字 - 使用导入的工具函数
    formatNumber,

    // 格式化时间 - 使用导入的工具函数
    formatTime,

    // 导航方法
    goToCreate() {
      uni.navigateTo({
        url: "/communitys/imagetext/create",
      });
    },

    goToDetail(imagetextId) {
      uni.navigateTo({
        url: `/communitys/imagetext/detail?id=${imagetextId}`,
      });
    },

    // 预览封面图片
    previewCoverImage(item) {
      const imageUrl = item.coverImageUrl;
      if (!imageUrl || imageUrl === "/static/images/placeholder.png") {
        uni.showToast({
          title: "暂无封面图片",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        success: () => {
          console.log("封面图片预览成功");
        },
        fail: (error) => {
          console.error("封面图片预览失败:", error);
          uni.showToast({
            title: "图片预览失败",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },

    // 预览作者头像
    previewAvatar(item) {
      const avatarUrl = item.avatar;
      if (!avatarUrl || avatarUrl === "/static/images/placeholder.png") {
        uni.showToast({
          title: "暂无头像图片",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      uni.previewImage({
        urls: [avatarUrl],
        current: avatarUrl,
        success: () => {
          console.log("头像预览成功");
        },
        fail: (error) => {
          console.error("头像预览失败:", error);
          uni.showToast({
            title: "图片预览失败",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.imagetext-list-container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.search-section {
  padding: 20rpx 30rpx;
  background: #fff;
}

.filter-section {
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.filter-tabs {
  display: flex;
  padding: 0 30rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: color 0.3s;

  &.active {
    color: #4caf50;
    font-weight: 500;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background: #4caf50;
      border-radius: 2rpx;
    }
  }
}

.imagetext-list {
  padding: 20rpx 30rpx;
}

.imagetext-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.item-images {
  width: 100%;
  height: 400rpx;
  position: relative;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.item-content {
  padding: 24rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.author-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 16rpx;
}

.author-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}

.author-name {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.item-stats {
  display: flex;
  gap: 40rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 120rpx 60rpx;
  color: #999;
}

.empty-text {
  display: block;
  margin: 30rpx 0;
  font-size: 28rpx;
}

.empty-action {
  margin-top: 40rpx;
}

.create-btn {
  background: #4caf50;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;

  &::after {
    border: none;
  }
}

.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  z-index: 999;
}

.fab-button {
  width: 112rpx;
  height: 112rpx;
  background: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.3);
  transition: transform 0.2s, box-shadow 0.2s;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.4);
  }
}
</style>
