<template>
  <view class="chat-page">
    <!-- 固定在顶部的热门问题区域 -->
    <view class="fixed-top">
      <view class="hot-band">
        <view class="hot-header">
          <text class="hot-title">猜你想搜</text>
          <text class="refresh" @click="shuffleHot">换一批</text>
        </view>
        <view class="hot-bubbles">
          <text v-for="item in hotList" :key="item.knowledgeId" class="hot-bubble" @click="sendQuestion(item)">
            {{ item.keyword }}
          </text>
        </view>
      </view>
    </view>

    <!-- 聊天区域 -->
    <scroll-view class="chat-scroller" scroll-y :scroll-top="scrollTop" scroll-with-animation :scroll-into-view="scrollToView">
      <view class="bubble-wrap">
        <view v-for="(msg, i) in chatList" :key="i" class="bubble" :class="msg.isUser ? 'right' : 'left'">
          <text class="txt">{{ msg.txt }}</text>
        </view>
        <view v-if="botTyping" class="bubble left typing" id="lastMsg">
          <text class="txt">查询中</text>
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
        <!-- 添加一个空元素用于滚动定位 -->
        <view id="bottom-anchor"></view>
      </view>
    </scroll-view>

    <!-- 搜索结果 -->
    <view v-if="resultList.length" class="result-band">
      <view v-for="item in resultList" :key="item.knowledgeId" class="result-item" @click="sendQuestion(item)">
        <rich-text class="result-txt" :nodes="highlight(item.question)"></rich-text>
      </view>
    </view>

    <!-- 底部发送栏 -->
    <view class="send-bar">
      <input v-model="keyword" class="send-input" placeholder="输入问题" confirm-type="send" @input="onInput" @confirm="onSend" />
      <view class="send-btn" @click="onSend">发送</view>
    </view>
  </view>
</template>

<script>
import { getKnowledgeList } from '@/service/api/aiknowledge.js';

export default {
  data() {
    return {
      keyword: '',
      chatList: [],
      botTyping: false,
      allKnowledgeList: [],
      hotList: [],
      resultList: [],
      scrollTop: 0,
      scrollToView: 'bottom-anchor' // 用于滚动到底部的锚点
    };
  },
  onLoad() {
    this.loadAllKnowledge();
    this.sendMessage('您好，您可以通过关键词或关键字来检索信息，例如：水、光照等词汇。', false);
  },
  methods: {
    async loadAllKnowledge() {
      const res = await getKnowledgeList('');
      if (res.code === 200) {
        this.allKnowledgeList = res.rows;
        this.shuffleHot();
      }
    },
    shuffleHot() {
      const pool = [...this.allKnowledgeList].sort(() => 0.5 - Math.random());
      this.hotList = pool.slice(0, 4);
    },
    onInput() {
      if (!this.keyword.trim()) {
        this.resultList = [];
        return;
      }
      
      // 提取关键词：去除疑问词和标点符号
      const kw = this.extractKeywords(this.keyword);
      
      // 使用提取后的关键词进行搜索
      this.resultList = this.allKnowledgeList.filter(item =>
        this.doesQuestionMatch(item.question, kw)
      );
    },
    
    // 提取关键词方法：去除疑问词和标点符号
    extractKeywords(input) {
      // 常见疑问词列表
      const questionWords = ['请问', '我想问', '什么是', '什么是', '怎么', '如何', '为什么', '哪儿', '哪里', '谁', '哪些', '多少', '是不是', '有没有'];
      
      // 去除标点符号
      let cleaned = input.replace(/[.,?!，。？！]/g, '');
      
      // 去除常见疑问词
      questionWords.forEach(word => {
        cleaned = cleaned.replace(new RegExp(word, 'g'), '');
      });
      
      // 去除多余空格并返回
      return cleaned.trim().toLowerCase();
    },
    
    // 改进的匹配方法：使用关键词而非完整匹配
    doesQuestionMatch(question, keywords) {
      // 如果关键词为空，返回false
      if (!keywords) return false;
      
      // 将问题转换为小写以便比较
      const qLower = question.toLowerCase();
      
      // 如果关键词包含空格，拆分为多个关键词
      const keywordList = keywords.split(/\s+/).filter(k => k.length > 0);
      
      // 如果只有一个关键词，直接检查是否包含
      if (keywordList.length === 1) {
        return qLower.includes(keywordList[0]);
      }
      
      // 如果有多个关键词，检查是否包含任意一个
      return keywordList.some(keyword => qLower.includes(keyword));
    },
    
    sendQuestion(item) {
      this.sendMessage(item.question, true);
      // 添加一点延迟，让UI有时间更新
      setTimeout(() => {
        this.sendMessage(item.answer, false);
      }, 100);
      this.resultList = [];
      this.keyword = '';
    },
    
    onSend() {
      if (!this.keyword.trim()) return;
      this.sendMessage(this.keyword, true);
      
      // 提取关键词进行匹配
      const kw = this.extractKeywords(this.keyword);
      const found = this.allKnowledgeList.find(item =>
        this.doesQuestionMatch(item.question, kw)
      );
      
      // 添加一点延迟，让UI有时间更新
      setTimeout(() => {
        if (found) {
          this.sendMessage(found.answer, false);
        } else {
          this.sendMessage('抱歉，暂无相关答案~', false);
        }
      }, 100);
      
      this.resultList = [];
      this.keyword = '';
    },
    
    highlight(text) {
      if (!this.keyword) return text;
      
      // 提取关键词用于高亮
      const kw = this.extractKeywords(this.keyword);
      if (!kw) return text;
      
      // 将关键词拆分为多个部分
      const keywordList = kw.split(/\s+/).filter(k => k.length > 0);
      
      // 为每个关键词添加高亮
      let highlighted = text;
      keywordList.forEach(keyword => {
        const reg = new RegExp(`(${keyword})`, 'gi');
        highlighted = highlighted.replace(reg, '<span style="color:#007aff">$1</span>');
      });
      
      return highlighted;
    },
    
    sendMessage(txt, isUser = false) {
      this.chatList.push({ txt, isUser });
      
      // 修复滚动问题：使用scroll-into-view和锚点元素
      this.$nextTick(() => {
        // 设置滚动到锚点元素
        this.scrollToView = 'bottom-anchor';
        
        // 同时设置scrollTop作为备用方案
        setTimeout(() => {
          const query = uni.createSelectorQuery().in(this);
          query.select('.chat-scroller').boundingClientRect(data => {
            if (data) {
              this.scrollTop = data.height;
            }
          }).exec();
        }, 50);
      });
    }
  }
};
</script>

<style scoped>
/* 样式保持不变，与之前相同 */
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f7fa;
}

.fixed-top {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #f5f7fa;
  padding: 20rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.hot-band {
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.hot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.hot-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.refresh {
  font-size: 26rpx;
  color: #007aff;
}

.hot-bubbles {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-bubble {
  background: #f0f7ff;
  color: #007aff;
  padding: 14rpx 24rpx;
  border-radius: 28rpx;
  font-size: 26rpx;
}

.chat-scroller {
  flex: 1;
  padding: 20rpx;
  padding-top: 10rpx;
}

.bubble-wrap {
  display: flex;
  flex-direction: column;
}

.bubble {
  max-width: 75%;
  padding: 22rpx 28rpx;
  margin: 20rpx 0;
  font-size: 30rpx;
  line-height: 1.6;
  border-radius: 20rpx;
  word-break: break-word;
  animation: slideIn 0.3s ease;
}

.bubble.right {
  align-self: flex-end;
  background: linear-gradient(135deg, #6abf8e, #5aa87c);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(106, 191, 142, 0.3);
}

.bubble.left {
  align-self: flex-start;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.typing {
  display: flex;
  align-items: center;
}

.dot {
  width: 10rpx;
  height: 10rpx;
  margin-left: 8rpx;
  background: #007aff;
  border-radius: 50%;
  animation: blink 1.4s infinite both;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

.result-band {
  background: #fff;
  max-height: 300rpx;
  overflow-y: auto;
  border-top: 1rpx solid #f2f2f2;
}

.result-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f2f2f2;
}

.result-txt {
  font-size: 30rpx;
  color: #333;
}

.send-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  background: #fff;
  border-top: 1rpx solid #f2f2f2;
}

.send-input {
  flex: 1;
  height: 72rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  background: #f8f9fa;
  border-radius: 36rpx;
  border: 2rpx solid #eaeaea;
}

.send-btn {
  margin-left: 20rpx;
  padding: 0 40rpx;
  line-height: 72rpx;
  background: linear-gradient(135deg, #6abf8e, #5aa87c);
  color: #fff;
  border-radius: 36rpx;
  font-size: 30rpx;
  font-weight: 500;
}

@keyframes blink {
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

#bottom-anchor {
  height: 0;
  width: 0;
  opacity: 0;
}
</style>