<template>
  <view class="imagetext-detail-container">
    <!-- 内容区域 -->
    <scroll-view
      scroll-y
      class="content-scroll"
      @scrolltolower="onScrollToLower"
      :scroll-top="scrollTop"
    >
      <view v-if="imagetextDetail" class="detail-content">
        <!-- 作者信息 -->
        <view class="author-section">
          <view class="author-info">
            <image
              :src="imagetextDetail.avatar || '/static/images/placeholder.png'"
              class="author-avatar"
              @click.stop="previewAvatar"
            ></image>
            <view class="author-text">
              <view class="author-name">{{ imagetextDetail.nickName }}</view>
              <view class="publish-time">{{
                formatTime(imagetextDetail.createTime)
              }}</view>
            </view>
          </view>
          <view class="author-actions">
            <button v-if="!isOwner" class="follow-btn" @click="toggleFollow">
              {{ isFollowing ? "已关注" : "关注" }}
            </button>
            <view class="more-btn" @click="showMore">
              <uni-icons type="more-filled" size="20" color="#666"></uni-icons>
            </view>
          </view>
        </view>

        <!-- 标题 -->
        <view class="content-title">{{ imagetextDetail.imagetextTitle }}</view>

        <!-- 正文内容 -->
        <view class="content-text">{{ imagetextDetail.imagetextContent }}</view>

        <!-- 图片展示 -->
        <view
          v-if="imagetextDetail.images && imagetextDetail.images.length"
          class="content-images"
        >
          <view
            v-for="(image, index) in imagetextDetail.images"
            :key="image.imageId"
            class="image-item"
          >
            <image
              :src="image.imageUrl"
              mode="widthFix"
              class="content-image"
              @click.stop="previewContentImages(index)"
            ></image>
          </view>
        </view>

        <!-- 统计信息 -->
        <view class="stats-section">
          <view class="stat-item">
            <uni-icons type="eye" size="18" color="#999"></uni-icons>
            <text>{{ formatNumber(imagetextDetail.viewCount) }} 阅读</text>
          </view>
          <view class="stat-item">
            <uni-icons type="heart" size="18" color="#999"></uni-icons>
            <text>{{ formatNumber(imagetextDetail.likeCount) }} 点赞</text>
          </view>
          <view class="stat-item">
            <uni-icons type="chat" size="18" color="#999"></uni-icons>
            <text>{{ formatNumber(imagetextDetail.commentCount) }} 评论</text>
          </view>
        </view>

        <!-- 互动区域 -->
        <view class="action-section">
          <view class="action-item" @click="toggleLike">
            <uni-icons
              :type="isLiked ? 'heart-filled' : 'heart'"
              size="24"
              :color="isLiked ? '#ff4757' : '#999'"
            ></uni-icons>
            <text :class="{ liked: isLiked }">{{
              isLiked ? "已点赞" : "点赞"
            }}</text>
          </view>
          <view class="action-item" @click="scrollToComment">
            <uni-icons type="chat" size="24" color="#999"></uni-icons>
            <text>评论</text>
          </view>
          <view class="action-item" @click="shareContent">
            <uni-icons type="redo" size="24" color="#999"></uni-icons>
            <text>分享</text>
          </view>
        </view>

        <!-- 楼中楼评论区域 -->
        <view class="comment-section" id="commentSection">
          <NestedComment
            ref="nestedComment"
            :key="`nested-comment-${imagetextId}`"
            :imageTextId="imagetextId"
            :commentList="nestedCommentList"
            :scrollToCommentId="scrollToCommentId"
            @submit-comment="handleSubmitComment"
            @submit-reply="handleSubmitReply"
            @like-comment="handleLikeComment"
            @load-more-replies="handleLoadMoreReplies"
            @load-sub-comments="handleLoadSubComments"
          />
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-else-if="loading" class="loading-state">
        <uni-load-more status="loading"></uni-load-more>
      </view>

      <!-- 错误状态 -->
      <view v-else class="error-state">
        <uni-icons type="info" size="80" color="#ccc"></uni-icons>
        <text class="error-text">内容加载失败</text>
        <button class="retry-btn" @click="loadDetail">重新加载</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {
  getImageTextDetail,
  getImageTextDetailWithView,
  getImageTextComments,
  createImageTextComment,
  deleteImageText,
  toggleLike,
  checkLike,
  getNestedComments,
  createTopLevelComment,
  createReplyComment,
  getMoreReplies,
  deleteNestedComment,
  toggleCommentLike,
} from "@/api/community.js";
import { formatTime, formatNumber } from "@/utils/timeUtils.js";
import NestedComment from "@/components/NestedComment.vue";
// import LazyImageV2 from "@/components/LazyImageV2.vue";
import {
  connectWebSocket,
  subscribeToCommentNotifications,
  subscribeToContentCommentUpdates,
  unsubscribeFromContentCommentUpdates,
  registerMessageHandler,
  removeMessageHandler,
  isConnected,
} from "@/utils/websocketManager.js";

export default {
  name: "ImageTextDetail",
  components: {
    NestedComment,
    // LazyImageV2,
  },
  data() {
    return {
      imagetextId: null,
      imagetextDetail: null,
      commentList: [], // 保留原有评论列表（兼容）
      nestedCommentList: [], // 新的楼中楼评论列表
      commentText: "",
      loading: false,
      commentLoading: false,
      commentLoadStatus: "more",
      commentPageNum: 1,
      scrollToCommentId: null, // 需要滚动到的评论ID
      scrollTop: 0, // 控制scroll-view的滚动位置
      loadingMoreComments: false, // 是否正在加载更多评论
      hasTriedLoadMoreReplies: false, // 是否已经尝试过加载更多回复
      commentPageSize: 10,
      commentHasMore: true,
      isLiked: false,
      isFollowing: false,
    };
  },

  computed: {
    // 判断是否为内容作者
    isOwner() {
      const userData = uni.getStorageSync("userData");
      return (
        userData &&
        userData.userId &&
        this.imagetextDetail &&
        userData.userId === this.imagetextDetail.userId
      );
    },
  },

  onLoad(options) {
    if (options.id) {
      this.imagetextId = options.id;

      // 处理智能跳转的评论ID参数
      if (options.commentId) {
        this.scrollToCommentId = parseInt(options.commentId);
        console.log("🎯 [智能跳转] 需要滚动到评论ID:", this.scrollToCommentId);
      }

      this.loadDetail();
      this.loadNestedComments(); // 加载楼中楼评论

      // 监听刷新事件
      uni.$on("refreshImageTextDetail", (imagetextId) => {
        if (imagetextId === this.imagetextId) {
          this.loadDetail();
          this.loadNestedComments();
        }
      });

      // 初始化实时评论通知
      this.initCommentNotification();
    } else {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },

  onUnload() {
    // 移除事件监听
    uni.$off("refreshImageTextDetail");

    // 清理实时评论通知
    this.cleanupCommentNotification();
  },

  methods: {
    // 加载详情
    async loadDetail() {
      if (!this.imagetextId) return;

      try {
        this.loading = true;
        const userData = uni.getStorageSync("userData");

        // 如果有用户信息，使用记录浏览量的接口
        let response;
        if (userData && userData.userId) {
          response = await getImageTextDetailWithView(
            this.imagetextId,
            userData.userId
          );
        } else {
          response = await getImageTextDetail(this.imagetextId);
        }

        if (response.code === 200) {
          this.imagetextDetail = response.data;
          console.log("图文详情数据:", this.imagetextDetail);
          console.log("评论数量:", this.imagetextDetail.commentCount);
          console.log("浏览数量:", this.imagetextDetail.viewCount);
          // 这里可以检查用户是否已点赞、关注等状态
          this.checkUserActions();
        } else {
          uni.showToast({
            title: response.msg || "加载失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("加载图文详情失败:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载评论
    async loadComments() {
      if (!this.imagetextId || this.commentLoading) return;

      try {
        this.commentLoading = true;
        this.commentLoadStatus = "loading";

        const response = await getImageTextComments(
          this.imagetextId,
          this.commentPageNum,
          this.commentPageSize
        );

        if (response.code === 200) {
          const newComments = response.rows || [];

          if (this.commentPageNum === 1) {
            this.commentList = newComments;
          } else {
            this.commentList = this.commentList.concat(newComments);
          }

          this.commentHasMore = newComments.length >= this.commentPageSize;
          this.commentLoadStatus = this.commentHasMore ? "more" : "noMore";
        } else {
          this.commentLoadStatus = "more";
        }
      } catch (error) {
        console.error("加载评论失败:", error);
        this.commentLoadStatus = "more";
      } finally {
        this.commentLoading = false;
      }
    },

    // 加载更多评论
    loadMoreComments() {
      if (!this.commentHasMore || this.commentLoading) return;
      this.commentPageNum++;
      this.loadComments();
    },

    // 提交评论
    async submitComment() {
      if (!this.commentText.trim()) return;

      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        const response = await createImageTextComment({
          imageTextId: this.imagetextId, // 修正字段名匹配后端实体类
          userId: userData.userId,
          commentContent: this.commentText.trim(),
        });

        if (response.code === 200) {
          this.commentText = "";
          // 重新加载评论列表
          this.commentPageNum = 1;
          this.loadComments();
          // 重新加载详情以获取最新的评论数量
          this.loadDetail();
          uni.showToast({
            title: "评论成功",
            icon: "success",
          });
        } else {
          uni.showToast({
            title: response.msg || "评论失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("提交评论失败:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      }
    },

    // 检查用户操作状态（点赞、关注等）
    async checkUserActions() {
      const userData = uni.getStorageSync("userData");
      if (userData && userData.userId) {
        try {
          // 检查点赞状态
          const likeResponse = await checkLike(
            "imagetext",
            this.imagetextId,
            userData.userId
          );
          if (likeResponse.code === 200) {
            this.isLiked = likeResponse.isLiked || false;
            // 同时更新点赞数量
            if (this.imagetextDetail && likeResponse.likeCount !== undefined) {
              this.imagetextDetail.likeCount = likeResponse.likeCount;
            }
          }
        } catch (error) {
          console.error("检查点赞状态失败:", error);
          // 如果API失败，回退到本地存储
          const likedKey = `liked_imagetext_${this.imagetextId}_${userData.userId}`;
          this.isLiked = uni.getStorageSync(likedKey) || false;
        }

        // 关注状态暂时使用本地存储
        const followingKey = `following_user_${this.imagetextDetail.userId}_${userData.userId}`;
        this.isFollowing = uni.getStorageSync(followingKey) || false;
      }
    },

    // 切换点赞
    async toggleLike() {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        // 调用API切换点赞状态
        const response = await toggleLike(
          "imagetext",
          this.imagetextId,
          userData.userId
        );

        if (response.code === 200) {
          // 更新点赞状态
          this.isLiked = response.isLiked;

          // 更新点赞数量
          if (this.imagetextDetail && response.likeCount !== undefined) {
            this.imagetextDetail.likeCount = response.likeCount;
          }

          uni.showToast({
            title: this.isLiked ? "点赞成功" : "取消点赞",
            icon: "success",
          });
        } else {
          uni.showToast({
            title: response.msg || "操作失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("点赞操作失败:", error);
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      }
    },

    // 切换关注
    toggleFollow() {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      this.isFollowing = !this.isFollowing;

      // 更新本地存储
      const followingKey = `following_user_${this.imagetextDetail.userId}_${userData.userId}`;
      uni.setStorageSync(followingKey, this.isFollowing);

      uni.showToast({
        title: this.isFollowing ? "关注成功" : "取消关注",
        icon: "success",
      });

      // 这里可以调用API更新服务器端的关注状态
    },

    // 预览图片
    previewImage(index) {
      if (!this.imagetextDetail.images) return;

      const urls = this.imagetextDetail.images.map((img) => img.imageUrl);
      uni.previewImage({
        current: index,
        urls: urls,
      });
    },

    // 滚动到评论输入框
    scrollToComment() {
      console.log("🎯 滚动到评论输入框位置");
      this.tryScrollToInput(0); // 开始重试机制
    },

    // 滚动到指定评论ID（直接测量实际位置）
    scrollToCommentById(commentId) {
      console.log("🎯 [智能跳转] 直接测量实际位置，评论ID:", commentId);

      // 直接测量评论107的实际位置
      const query = uni.createSelectorQuery().in(this.$refs.nestedComment);
      query.select(`#comment-${commentId}`).boundingClientRect();

      // 同时获取scroll-view的信息
      const globalQuery = uni.createSelectorQuery();
      globalQuery.select(".content-scroll").boundingClientRect();
      globalQuery.select(".content-scroll").scrollOffset();

      query.exec((commentRes) => {
        globalQuery.exec((globalRes) => {
          if (commentRes[0] && globalRes[0] && globalRes[1]) {
            const commentRect = commentRes[0];
            const scrollViewRect = globalRes[0];
            const scrollOffset = globalRes[1];

            // 计算评论在scroll-view中的相对位置
            const relativeTop =
              commentRect.top - scrollViewRect.top + scrollOffset.scrollTop;
            const targetScrollTop = relativeTop - 100; // 留100px空间

            console.log(
              "🎯 [智能跳转] 找到评论，实际位置:",
              relativeTop,
              "目标位置:",
              targetScrollTop
            );
            this.scrollTop = targetScrollTop;
          } else {
            console.log("🎯 [智能跳转] 未找到评论，滚动到评论区顶部");
            this.scrollToComment();
          }
        });
      });
    },

    // 查找评论的父评论
    findParentComment(commentId) {
      for (let i = 0; i < this.nestedCommentList.length; i++) {
        const comment = this.nestedCommentList[i];

        // 检查回复
        if (comment.replies) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            if ((reply.commentId || reply.replyId) == commentId) {
              return comment; // 返回父评论
            }

            // 检查子回复
            if (reply.replies) {
              for (let k = 0; k < reply.replies.length; k++) {
                const subReply = reply.replies[k];
                if ((subReply.commentId || subReply.replyId) == commentId) {
                  return comment; // 返回父评论
                }
              }
            }
          }
        }
      }

      return null;
    },

    // 尝试滚动到输入框（带重试机制）
    tryScrollToInput(retryCount) {
      const maxRetries = 5; // 最多重试5次
      const retryDelay = 300; // 每次重试间隔300ms

      console.log(`🔍 第${retryCount + 1}次尝试查找输入框`);

      // 等待组件渲染
      this.$nextTick(() => {
        setTimeout(
          () => {
            // 通过组件引用查找输入框
            const inputQuery = uni
              .createSelectorQuery()
              .in(this.$refs.nestedComment);
            inputQuery.select("#commentInput").boundingClientRect();

            // 全局查找其他元素
            const globalQuery = uni.createSelectorQuery();
            globalQuery.select("#commentSection").boundingClientRect(); // 全局查找评论区
            globalQuery.select(".content-scroll").boundingClientRect(); // 全局查找scroll-view
            globalQuery.select(".content-scroll").scrollOffset(); // 全局查找scroll-view滚动位置

            // 先查询输入框
            inputQuery.exec((inputRes) => {
              // 再查询其他元素
              globalQuery.exec((globalRes) => {
                const res = [...inputRes, ...globalRes]; // 合并结果
                const inputRect = res[0]; // 输入框位置
                const commentRect = res[1]; // 评论区位置（备用）
                const scrollViewRect = res[2]; // scroll-view位置
                const scrollViewOffset = res[3]; // scroll-view滚动位置

                console.log(`🔍 第${retryCount + 1}次查询结果:`, {
                  输入框: inputRect ? "✅找到" : "❌未找到",
                  评论区: commentRect ? "✅找到" : "❌未找到",
                });

                // 如果找到输入框，优先使用输入框
                if (inputRect && scrollViewRect && scrollViewOffset) {
                  console.log("✅ 找到输入框，开始精确滚动");
                  const relativeTop =
                    inputRect.top -
                    scrollViewRect.top +
                    scrollViewOffset.scrollTop;
                  const targetScrollTop = relativeTop - 80; // 留80px空间

                  console.log("🎯 滚动到输入框，目标位置:", targetScrollTop);
                  this.scrollTop = targetScrollTop;

                  // 显示提示
                  setTimeout(() => {
                    if (this.$refs.nestedComment) {
                      this.$refs.nestedComment.focusInput();
                    }
                  }, 200);
                  return;
                }

                // 如果没找到输入框但还有重试次数，继续重试
                if (!inputRect && retryCount < maxRetries) {
                  console.log(
                    `⏳ 输入框未找到，${retryDelay}ms后进行第${
                      retryCount + 2
                    }次重试`
                  );
                  setTimeout(() => {
                    this.tryScrollToInput(retryCount + 1);
                  }, retryDelay);
                  return;
                }

                // 重试次数用完，使用评论区作为备用
                if (commentRect && scrollViewRect && scrollViewOffset) {
                  console.log("⚠️ 重试次数用完，使用评论区作为备用目标");
                  const relativeTop =
                    commentRect.top -
                    scrollViewRect.top +
                    scrollViewOffset.scrollTop;
                  const targetScrollTop = relativeTop - 50;

                  console.log(
                    "🎯 滚动到评论区（备用），目标位置:",
                    targetScrollTop
                  );
                  this.scrollTop = targetScrollTop;

                  // 显示提示
                  setTimeout(() => {
                    if (this.$refs.nestedComment) {
                      this.$refs.nestedComment.focusInput();
                    }
                  }, 200);
                } else {
                  console.log("❌ 无法获取任何位置信息");
                }
              });
            });
          },
          retryCount === 0 ? 300 : 100
        ); // 第一次等待更久，后续重试等待时间短一些
      });
    },

    // 分享内容
    shareContent() {
      uni.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        href: `https://yourapp.com/imagetext/${this.imagetextId}`,
        title: this.imagetextDetail.imagetextTitle,
        summary: this.imagetextDetail.imagetextContent,
        imageUrl: this.imagetextDetail.coverImageUrl,
        success: () => {
          uni.showToast({
            title: "分享成功",
            icon: "success",
          });
        },
        fail: () => {
          // 降级到复制链接
          uni.setClipboardData({
            data: `${this.imagetextDetail.imagetextTitle} - 来自绿境智慧`,
            success: () => {
              uni.showToast({
                title: "内容已复制",
                icon: "success",
              });
            },
          });
        },
      });
    },

    // 显示更多操作
    showMore() {
      const itemList = this.isOwner
        ? ["编辑", "删除"] // 作者只能编辑和删除自己的内容
        : ["收藏", "举报"]; // 其他用户可以收藏和举报

      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const action = itemList[res.tapIndex];
          switch (action) {
            case "编辑":
              this.editImagetext();
              break;
            case "删除":
              this.deleteImagetext();
              break;
            case "收藏":
              this.collectImagetext();
              break;
            case "举报":
              this.reportImagetext();
              break;
          }
        },
      });
    },

    // 编辑图文
    editImagetext() {
      uni.navigateTo({
        url: `/communitys/imagetext/edit?id=${this.imagetextId}`,
      });
    },

    // 删除图文
    async deleteImagetext() {
      uni.showModal({
        title: "确认删除",
        content: "确定要删除这篇图文分享吗？删除后无法恢复。",
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: "删除中...",
              });

              const userData = uni.getStorageSync("userData");

              // 调用删除API
              const response = await deleteImageText(
                this.imagetextId,
                userData.userId
              );

              if (response.code === 200) {
                uni.hideLoading();
                uni.showToast({
                  title: "删除成功",
                  icon: "success",
                });

                // 通知相关页面刷新数据
                uni.$emit("refreshImageTextList");
                uni.$emit("refreshCommunity");

                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              } else {
                uni.hideLoading();
                uni.showToast({
                  title: response.msg || "删除失败",
                  icon: "none",
                });
              }
            } catch (error) {
              uni.hideLoading();
              console.error("删除图文失败:", error);
              uni.showToast({
                title: "网络错误，请重试",
                icon: "none",
              });
            }
          }
        },
      });
    },

    // 收藏图文
    collectImagetext() {
      uni.showToast({
        title: "收藏成功",
        icon: "success",
      });
    },

    // 举报图文
    reportImagetext() {
      uni.navigateTo({
        url: "/pages/common/report",
      });
    },

    // 格式化数字 - 使用导入的工具函数
    formatNumber,

    // 格式化时间 - 使用导入的工具函数
    formatTime,

    // 判断评论者是否为图文作者
    isCommentAuthor(comment) {
      return (
        this.imagetextDetail && comment.userId === this.imagetextDetail.userId
      );
    },

    // 页面滚动到底部
    onScrollToLower() {
      // 可以在这里处理一些滚动到底部的逻辑
    },

    // ==================== 楼中楼评论相关方法 ====================

    // 将后端返回的嵌套结构转换为前端期望的扁平化结构
    convertNestedToFlat(nestedData) {
      const flatList = [];

      for (const topComment of nestedData) {
        // 添加顶级评论
        const flatTopComment = {
          ...topComment,
          type: 1, // 确保顶级评论的type为1
          parentId: null, // 确保顶级评论的parentId为null
        };
        flatList.push(flatTopComment);

        // 处理二层和三层回复
        if (topComment.replies && topComment.replies.length > 0) {
          for (const level2Reply of topComment.replies) {
            // 添加二层回复
            const flatLevel2Reply = {
              ...level2Reply,
              type: 2, // 确保二层回复的type为2
              parentId: topComment.commentId, // 确保parentId指向顶级评论
            };
            flatList.push(flatLevel2Reply);

            // 处理三层回复
            if (level2Reply.replies && level2Reply.replies.length > 0) {
              for (const level3Reply of level2Reply.replies) {
                // 添加三层回复
                const flatLevel3Reply = {
                  ...level3Reply,
                  type: 3, // 确保三层回复的type为3
                  parentId: level2Reply.commentId, // 确保parentId指向二层回复
                };
                flatList.push(flatLevel3Reply);
              }
            }
          }
        }
      }

      console.log(
        `🔍 [数据转换] 转换完成: ${nestedData.length}个顶级评论 -> ${flatList.length}条扁平化记录`
      );
      return flatList;
    },

    // 加载楼中楼评论
    async loadNestedComments() {
      if (!this.imagetextId) return;

      try {
        this.commentLoading = true;
        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        const response = await getNestedComments(
          this.imagetextId,
          currentUserId,
          this.commentPageNum
        );

        if (response.code === 200) {
          const nestedData = response.data || [];
          console.log("🔍 [数据转换] 后端返回的嵌套数据:", nestedData);

          // 将后端返回的嵌套结构转换为前端期望的扁平化结构
          this.nestedCommentList = this.convertNestedToFlat(nestedData);
          console.log(
            "🔍 [数据转换] 转换后的扁平化数据:",
            this.nestedCommentList
          );

          // 如果有需要定位的评论ID，自动展开所有折叠的评论
          if (this.scrollToCommentId) {
            console.log("🎯 [智能跳转] 需要定位评论，自动展开所有折叠");

            // 使用 $nextTick 确保数据更新后再展开
            this.$nextTick(() => {
              this.ensureAllCommentsExpanded();

              // 再次使用 $nextTick 确保展开完成后再滚动
              this.$nextTick(() => {
                this.scrollToTargetComment();
              });
            });
          }
        } else {
          console.error("加载评论失败:", response.msg);
        }
      } catch (error) {
        console.error("加载评论异常:", error);
      } finally {
        this.commentLoading = false;
      }
    },

    // 处理提交主评论
    async handleSubmitComment(data) {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        const response = await createTopLevelComment(
          this.imagetextId,
          userData.userId,
          data.content
        );

        if (response.code === 200) {
          uni.showToast({
            title: "评论成功",
            icon: "success",
          });

          // 智能更新：添加新的顶级评论到列表开头
          if (response.data) {
            this.nestedCommentList.unshift(response.data);
            console.log("🔄 [智能更新] 成功添加顶级评论");
          } else {
            // 如果没有返回数据，降级为重新加载
            this.loadNestedComments();
          }
          // 重新加载详情（更新评论数量）
          this.loadDetail();
        } else {
          uni.showToast({
            title: response.msg || "评论失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("提交评论异常:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 处理提交回复
    async handleSubmitReply(data) {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        return;
      }

      try {
        const replyData = {
          imageTextId: this.imagetextId,
          parentId: data.parentId,
          rootId: data.rootId,
          type: data.type,
          userId: userData.userId,
          replyToUserId: data.replyToUserId,
          replyToUserName: data.replyToUserName,
          originalCommentId: data.originalCommentId,
          commentContent: data.content,
        };

        const response = await createReplyComment(replyData);

        if (response.code === 200) {
          uni.showToast({
            title: "回复成功",
            icon: "success",
          });

          // 智能更新：不重新加载整个列表，而是添加新回复到对应位置
          this.addNewReplyToCommentList(response.data, data);
          // 确保用户能看到新回复
          this.ensureNewReplyVisible(response.data, data);

          // 临时解决方案：强制展开父评论的回复区域
          this.forceExpandParentComment(response.data, data);
          // 重新加载详情（更新评论数量）
          this.loadDetail();
        } else {
          uni.showToast({
            title: response.msg || "回复失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("提交回复异常:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 处理点赞评论
    async handleLikeComment(data) {
      try {
        const response = await toggleCommentLike(data.commentId, data.userId);

        if (response.code === 200) {
          // 更新本地数据
          const comment = data.comment;
          comment.isLiked = response.isLiked;
          comment.likeCount = response.likeCount;

          // 如果是顶级评论，在nestedCommentList中更新
          const topComment = this.nestedCommentList.find(
            (c) => c.commentId === data.commentId
          );
          if (topComment) {
            topComment.isLiked = response.isLiked;
            topComment.likeCount = response.likeCount;
          } else {
            // 如果是回复评论，在replies中查找并更新
            for (let topComment of this.nestedCommentList) {
              if (topComment.replies) {
                const reply = topComment.replies.find(
                  (r) => r.commentId === data.commentId
                );
                if (reply) {
                  reply.isLiked = response.isLiked;
                  reply.likeCount = response.likeCount;
                  break;
                }
              }
            }
          }

          uni.showToast({
            title: response.isLiked ? "点赞成功" : "取消点赞",
            icon: "success",
          });
        } else {
          uni.showToast({
            title: response.msg || "操作失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("点赞评论异常:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 处理加载更多回复
    async handleLoadMoreReplies(parentId) {
      try {
        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        const response = await getMoreReplies(parentId, currentUserId);

        if (response.code === 200) {
          // 找到对应的顶级评论，更新其回复列表
          const topComment = this.nestedCommentList.find(
            (comment) => comment.commentId === parentId
          );
          if (topComment) {
            topComment.replies = response.data || [];
          }
        }
      } catch (error) {
        console.error("加载更多回复异常:", error);
        uni.showToast({
          title: "加载失败",
          icon: "none",
        });
      }
    },

    // 处理按需加载子评论（图文分享版本）
    async handleLoadSubComments(parentId) {
      try {
        console.log("🔄 [按需加载] 开始加载子评论:", parentId);

        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        // 图文分享使用自己的API
        const response = await getMoreReplies(parentId, currentUserId);

        if (response.code === 200) {
          // 找到对应的顶级评论，更新其回复列表
          const topComment = this.nestedCommentList.find(
            (comment) => comment.commentId === parentId
          );
          if (topComment) {
            topComment.replies = response.data || [];
            console.log(
              "🔄 [按需加载] 子评论加载成功:",
              response.data?.length || 0,
              "条"
            );
          } else {
            console.warn("🔄 [按需加载] 未找到父评论:", parentId);
          }
        } else {
          console.error("🔄 [按需加载] 加载失败:", response.msg);
        }
      } catch (error) {
        console.error("🔄 [按需加载] 加载子评论异常:", error);
        uni.showToast({
          title: "加载失败",
          icon: "none",
        });
      }
    },

    // 智能添加新回复到评论列表（保持展开状态）
    addNewReplyToCommentList(newReply, originalData) {
      if (!newReply || !originalData) {
        console.warn("🔄 [智能更新] 缺少必要数据，降级为重新加载");
        this.loadNestedComments();
        return;
      }

      console.log("🔄 [智能更新] 添加新回复到评论列表:", newReply);
      console.log("🔄 [智能更新] 原始数据:", originalData);
      console.log("🔄 [智能更新] 新回复的ID字段:", {
        commentId: newReply.commentId,
        replyId: newReply.replyId,
        id: newReply.id,
      });

      try {
        // 根据回复类型处理
        if (originalData.type === 2) {
          // 回复顶级评论：添加到对应顶级评论的replies数组
          const parentComment = this.nestedCommentList.find(
            (comment) => comment.commentId === originalData.parentId
          );

          if (parentComment) {
            if (!parentComment.replies) {
              parentComment.replies = [];
            }
            parentComment.replies.push(newReply);
            parentComment.replyCount = (parentComment.replyCount || 0) + 1;
            console.log("🔄 [智能更新] 成功添加二级回复");
            console.log(
              "🔄 [智能更新] 父评论更新后的回复数量:",
              parentComment.replies.length
            );
            console.log(
              "🔄 [智能更新] 父评论更新后的replyCount:",
              parentComment.replyCount
            );
          } else {
            console.warn("🔄 [智能更新] 未找到父评论，降级为重新加载");
            this.loadNestedComments();
          }
        } else if (originalData.type === 3) {
          // 回复子评论：添加到对应顶级评论的replies数组
          const rootComment = this.nestedCommentList.find(
            (comment) => comment.commentId === originalData.rootId
          );

          if (rootComment) {
            if (!rootComment.replies) {
              rootComment.replies = [];
            }
            rootComment.replies.push(newReply);
            rootComment.replyCount = (rootComment.replyCount || 0) + 1;
            console.log("🔄 [智能更新] 成功添加三级回复");
          } else {
            console.warn("🔄 [智能更新] 未找到根评论，降级为重新加载");
            this.loadNestedComments();
          }
        } else {
          console.warn("🔄 [智能更新] 未知回复类型，降级为重新加载");
          this.loadNestedComments();
        }
      } catch (error) {
        console.error("🔄 [智能更新] 更新失败，降级为重新加载:", error);
        this.loadNestedComments();
      }
    },

    // 确保新回复对用户可见（图文分享版本）
    ensureNewReplyVisible(newReply, originalData) {
      if (!newReply || !originalData) return;

      console.log("🎯 [可见性确保] 开始确保新回复可见:", newReply);

      // 等待DOM更新
      this.$nextTick(() => {
        setTimeout(() => {
          const newReplyId = newReply.commentId;
          console.log("🎯 [可见性确保] 新回复ID:", newReplyId);

          // 检查新回复是否在当前可见区域
          if (this.isReplyCurrentlyVisible(newReplyId, originalData)) {
            console.log("🎯 [可见性确保] 新回复已在可见区域，直接定位");
            this.scrollToNewReply(newReplyId);
          } else {
            console.log("🎯 [可见性确保] 新回复不在可见区域，需要展开");
            this.expandAndScrollToNewReply(newReplyId, originalData);
          }
        }, 500); // 增加等待时间，确保DOM更新完成
      });
    },

    // 检查回复是否在当前可见区域（图文分享版本）
    isReplyCurrentlyVisible(replyId, originalData) {
      // 检查父评论是否在显示的评论列表中
      const parentCommentId =
        originalData.type === 2 ? originalData.parentId : originalData.rootId;

      // 如果是回复顶级评论，检查顶级评论是否可见
      if (originalData.type === 2) {
        // 检查顶级评论是否在当前显示的列表中
        const topCommentIndex = this.nestedCommentList.findIndex(
          (comment) => comment.commentId === parentCommentId
        );

        if (topCommentIndex === -1) return false;

        // 检查是否在折叠区域（前5条之外）
        if (this.nestedCommentList.length > 5 && topCommentIndex >= 5) {
          // 检查顶级评论是否展开
          const nestedComment = this.$refs.nestedComment;
          if (nestedComment && !nestedComment.topCommentsExpanded) {
            return false; // 在折叠区域且未展开
          }
        }

        // 检查父评论的回复是否展开
        const parentComment = this.nestedCommentList[topCommentIndex];
        console.log(
          "🔍 [可见性检测] 父评论回复数量:",
          parentComment.replies?.length || 0
        );
        console.log(
          "🔍 [可见性检测] 父评论replyCount:",
          parentComment.replyCount || 0
        );

        // 使用replyCount而不是replies.length，因为replies可能被截断显示
        const totalReplies =
          parentComment.replyCount || parentComment.replies?.length || 0;
        console.log("🔍 [可见性检测] 实际回复总数:", totalReplies);

        if (totalReplies > 3) {
          const nestedComment = this.$refs.nestedComment;
          const isExpanded = nestedComment?.foldedComments.has(parentCommentId);
          console.log("🔍 [可见性检测] 父评论回复展开状态:", isExpanded);
          console.log(
            "🔍 [可见性检测] foldedComments集合:",
            Array.from(nestedComment?.foldedComments || [])
          );
          if (nestedComment && !isExpanded) {
            console.log("🔍 [可见性检测] 父评论回复区域未展开，不可见");
            return false; // 回复区域未展开（不在集合中表示折叠）
          }
        }
      }

      return true; // 默认认为可见
    },

    // 展开并滚动到新回复（图文分享版本）
    expandAndScrollToNewReply(replyId, originalData) {
      console.log("🎯 [自动展开] 开始展开并定位到新回复:", replyId);

      const parentCommentId =
        originalData.type === 2 ? originalData.parentId : originalData.rootId;
      const parentCommentIndex = this.nestedCommentList.findIndex(
        (comment) => comment.commentId === parentCommentId
      );

      if (parentCommentIndex === -1) {
        console.warn("🎯 [自动展开] 未找到父评论，无法展开");
        return;
      }

      const nestedComment = this.$refs.nestedComment;
      if (!nestedComment) {
        console.warn("🎯 [自动展开] 未找到NestedComment组件");
        return;
      }

      // 如果父评论在折叠区域，先展开顶级评论
      if (
        this.nestedCommentList.length > 5 &&
        parentCommentIndex >= 5 &&
        !nestedComment.topCommentsExpanded
      ) {
        console.log("🎯 [自动展开] 展开顶级评论区域");
        nestedComment.toggleTopCommentsFold();
      }

      // 展开父评论的回复区域（如果还没展开的话）
      if (!nestedComment.foldedComments.has(parentCommentId)) {
        console.log("🎯 [自动展开] 展开父评论的回复区域:", parentCommentId);
        nestedComment.foldedComments.add(parentCommentId);
        // 强制更新视图
        nestedComment.$forceUpdate();
      }

      // 等待展开完成后滚动
      this.$nextTick(() => {
        setTimeout(() => {
          this.scrollToNewReply(replyId);
        }, 500);
      });
    },

    // 滚动到新回复（图文分享版本）- 修复版
    scrollToNewReply(replyId) {
      console.log("🎯 [滚动定位] 滚动到新回复:", replyId);

      // 增加多次尝试机制，确保DOM完全更新
      let attempts = 0;
      const maxAttempts = 5;

      const tryScroll = () => {
        attempts++;
        console.log(`🎯 [滚动定位] 第${attempts}次尝试滚动到回复:`, replyId);

        // 尝试多种可能的选择器格式
        const selectors = [
          `#comment-${replyId}`,
          `.comment-${replyId}`,
          `[data-comment-id="${replyId}"]`,
        ];

        // 使用第一个选择器尝试滚动
        const selector = selectors[0];
        console.log("🎯 [滚动定位] 使用选择器:", selector);

        uni.pageScrollTo({
          selector: selector,
          duration: 300,
          success: () => {
            console.log("🎯 [滚动定位] 滚动成功");
            this.highlightNewReply(replyId);
          },
          fail: (error) => {
            console.warn(`🎯 [滚动定位] 第${attempts}次滚动失败:`, error);

            // 如果还有尝试次数，继续尝试
            if (attempts < maxAttempts) {
              setTimeout(tryScroll, 300);
            } else {
              console.error("🎯 [滚动定位] 所有滚动尝试都失败，使用备用方案");
              this.fallbackScrollToReply(replyId);
            }
          },
        });
      };

      // 等待DOM更新后开始尝试
      this.$nextTick(() => {
        setTimeout(tryScroll, 200);
      });
    },

    // 高亮新回复
    highlightNewReply(replyId) {
      console.log("🎯 [高亮显示] 高亮新回复:", replyId);
      const nestedComment = this.$refs.nestedComment;
      if (nestedComment) {
        nestedComment.highlightedCommentId = replyId;
        setTimeout(() => {
          nestedComment.highlightedCommentId = null;
        }, 2000);
      }
    },

    // 备用滚动方案
    fallbackScrollToReply(replyId) {
      console.log("🎯 [备用滚动] 使用备用方案滚动到回复:", replyId);

      // 尝试滚动到评论区域
      uni.pageScrollTo({
        selector: ".nested-comment-container",
        duration: 300,
        success: () => {
          console.log("🎯 [备用滚动] 滚动到评论区域成功");
          this.highlightNewReply(replyId);
        },
        fail: () => {
          // 最后的备用方案：滚动到页面底部
          console.log("🎯 [备用滚动] 滚动到页面底部");
          uni.pageScrollTo({
            scrollTop: 99999,
            duration: 300,
          });
          this.highlightNewReply(replyId);
        },
      });
    },

    // 执行滚动操作
    performScroll(selector, replyId) {
      console.log("🎯 [滚动定位] 执行滚动操作:", selector);

      uni.pageScrollTo({
        selector: selector,
        duration: 500,
        success: () => {
          console.log("🎯 [滚动定位] 滚动成功");
          // 短暂高亮新回复
          const nestedComment = this.$refs.nestedComment;
          if (nestedComment) {
            nestedComment.highlightedCommentId = replyId;
            setTimeout(() => {
              nestedComment.highlightedCommentId = null;
            }, 2000);
          }
        },
        fail: (error) => {
          console.warn("🎯 [滚动定位] 滚动失败:", error);
        },
      });
    },

    // 强制展开父评论（临时解决方案）
    forceExpandParentComment(newReply, originalData) {
      console.log("🚀 [强制展开] 开始强制展开父评论:", originalData);

      if (originalData.type !== 2) {
        console.log("🚀 [强制展开] 不是二级回复，跳过");
        return;
      }

      const parentCommentId = originalData.parentId;
      const nestedComment = this.$refs.nestedComment;

      if (!nestedComment) {
        console.warn("🚀 [强制展开] 未找到NestedComment组件");
        return;
      }

      console.log("🚀 [强制展开] 父评论ID:", parentCommentId);
      console.log(
        "🚀 [强制展开] 展开前foldedComments:",
        Array.from(nestedComment.foldedComments)
      );

      // 强制展开父评论的回复区域
      nestedComment.foldedComments.add(parentCommentId);
      console.log(
        "🚀 [强制展开] 展开后foldedComments:",
        Array.from(nestedComment.foldedComments)
      );

      // 强制更新视图
      nestedComment.$forceUpdate();

      // 等待DOM更新后尝试滚动
      this.$nextTick(() => {
        setTimeout(() => {
          const newReplyId = newReply.commentId;
          console.log("🚀 [强制展开] 尝试滚动到新回复:", newReplyId);
          this.scrollToNewReply(newReplyId);
        }, 1000);
      });
    },

    // ========== WebSocket实时通知相关方法 ==========

    /**
     * 初始化评论通知
     */
    async initCommentNotification() {
      const userData = uni.getStorageSync("userData");
      if (!userData || !userData.userId) {
        return;
      }

      try {
        // 连接WebSocket (如果还没连接)
        if (!isConnected()) {
          await connectWebSocket(
            userData.userId.toString(),
            userData.token || ""
          );
        }

        // 个人评论通知现在由全局统一处理，这里只处理评论更新
        // 注释掉页面级的评论通知处理器，避免重复显示
        // registerMessageHandler('commentNotification', (message) => {
        //   console.log('[ImageTextDetail] 收到个人评论通知，自动刷新评论列表')
        //
        //   // 只自动刷新评论列表，不显示通知（通知由全局处理）
        //   this.loadNestedComments()
        // })

        // 注册评论更新处理器
        registerMessageHandler("commentUpdate", (data) => {
          console.log("🔥 [ImageTextDetail DEBUG] 收到评论更新:", data);
          console.log(
            "🔥 [ImageTextDetail DEBUG] data.contentType:",
            data.contentType
          );
          console.log(
            "🔥 [ImageTextDetail DEBUG] data.contentId:",
            data.contentId
          );
          console.log(
            "🔥 [ImageTextDetail DEBUG] this.imagetextId:",
            this.imagetextId
          );
          console.log(
            "🔥 [ImageTextDetail DEBUG] 类型匹配:",
            data.contentType === "imagetext"
          );
          console.log(
            "🔥 [ImageTextDetail DEBUG] ID匹配:",
            data.contentId == this.imagetextId
          );

          if (
            data.contentType === "imagetext" &&
            data.contentId == this.imagetextId
          ) {
            console.log(
              "🔥 [ImageTextDetail DEBUG] 条件匹配，静默刷新评论列表"
            );

            // 静默刷新评论列表（不显示提示）
            this.loadNestedComments();

            // 更新图文评论数
            if (this.imagetextDetail) {
              const oldCount = this.imagetextDetail.commentCount || 0;
              this.imagetextDetail.commentCount = oldCount + 1;
              console.log(
                "🔥 [ImageTextDetail DEBUG] 评论数更新:",
                oldCount,
                "->",
                this.imagetextDetail.commentCount
              );
            }

            // 注意：不显示Toast提示，因为这是广播更新
            // 只有个人通知（commentNotification）才显示提示
          } else {
            console.log("🔥 [ImageTextDetail DEBUG] 条件不匹配，忽略消息");
          }
        });

        // 个人评论通知已在App启动时全局订阅，这里不需要重复订阅
        // subscribeToCommentNotifications(userData.userId)

        // 订阅当前图文的评论更新
        subscribeToContentCommentUpdates("imagetext", this.imagetextId);

        console.log("[ImageTextDetail] WebSocket通知已初始化");
      } catch (error) {
        console.error("[ImageTextDetail] 初始化WebSocket通知失败:", error);
      }
    },

    /**
     * 清理评论通知
     */
    cleanupCommentNotification() {
      try {
        // 取消订阅当前图文的评论更新
        unsubscribeFromContentCommentUpdates("imagetext", this.imagetextId);

        // 移除消息处理器
        removeMessageHandler("commentNotification");
        removeMessageHandler("commentUpdate");

        console.log("[ImageTextDetail] WebSocket通知已清理");
      } catch (error) {
        console.error("[ImageTextDetail] 清理WebSocket通知失败:", error);
      }
    },

    /**
     * 显示评论通知
     */
    showCommentNotification(message) {
      if (message.fromUserName && message.content) {
        uni.showToast({
          title: `${message.fromUserName}评论了您`,
          icon: "none",
          duration: 3000,
        });
      }
    },

    /**
     * 滚动到目标评论
     */
    scrollToTargetComment() {
      if (!this.scrollToCommentId) return;

      console.log("🎯 [智能跳转] 开始滚动到评论ID:", this.scrollToCommentId);

      // 直接执行滚动，无需延迟
      this.performScrollToComment();
    },

    /**
     * 执行滚动到评论
     */
    performScrollToComment() {
      try {
        console.log(
          "🎯 [智能跳转] 执行滚动，查找评论ID:",
          this.scrollToCommentId
        );

        // 重置加载状态标志
        this.loadingMoreComments = false;
        this.hasTriedLoadMoreReplies = false;

        // 1. 先检查数据中是否存在目标评论
        const foundComment = this.findCommentInList(this.scrollToCommentId);
        if (!foundComment) {
          console.log("🎯 [智能跳转] 数据中未找到目标评论");
          if (!this.hasTriedLoadMoreReplies) {
            console.log("🎯 [智能跳转] 尝试加载更多回复");
            this.loadMoreRepliesToFindTarget(this.scrollToCommentId);
          } else {
            console.log("🎯 [智能跳转] 已经尝试过加载更多回复，停止查找");
            uni.showToast({
              title: "未找到目标评论",
              icon: "none",
              duration: 2000,
            });
          }
          return;
        }

        console.log("🎯 [智能跳转] 数据中找到目标评论，开始定位");

        // 2. 确保所有评论都已展开
        this.ensureAllCommentsExpanded();

        // 3. 等待组件更新后滚动
        this.$nextTick(() => {
          console.log(
            "🎯 [智能跳转] 开始滚动到评论ID:",
            this.scrollToCommentId
          );

          // 直接使用和评论图标一样的滚动方式
          this.scrollToCommentById(this.scrollToCommentId);

          console.log("🎯 [智能跳转] 滚动完成");
        });
      } catch (error) {
        console.error("🎯 [智能跳转] 滚动到目标评论异常:", error);
        this.scrollToCommentId = null;
      }
    },

    /**
     * 确保所有评论都已展开（数据驱动）
     */
    ensureAllCommentsExpanded() {
      console.log("🎯 [智能跳转] 确保所有评论都已展开");

      // 自动点击所有展开按钮
      this.clickAllExpandButtons();
    },

    // 自动点击所有展开按钮
    clickAllExpandButtons() {
      console.log("🎯 [智能跳转] 开始自动点击展开按钮");

      if (!this.$refs.nestedComment) {
        console.log("🎯 [智能跳转] 子组件引用不存在");
        return;
      }

      // 直接调用子组件的展开方法，确保所有评论都展开
      const nestedComment = this.$refs.nestedComment;

      // 1. 展开所有顶级评论
      if (nestedComment.commentList.length > 5) {
        console.log("🎯 [智能跳转] 展开所有顶级评论");
        nestedComment.expandAllTopComments();
      }

      // 2. 展开所有回复
      console.log("🎯 [智能跳转] 展开所有回复");
      nestedComment.expandAllReplies();

      // 3. 展开所有子回复
      console.log("🎯 [智能跳转] 展开所有子回复");
      nestedComment.expandAllSubReplies();

      console.log("🎯 [智能跳转] 所有展开按钮点击完成");
    },

    /**
     * 在评论列表中查找目标评论
     */
    findCommentInList(targetCommentId) {
      if (!targetCommentId || !this.nestedCommentList) return null;

      // 在顶级评论中查找
      for (let comment of this.nestedCommentList) {
        if ((comment.commentId || comment.replyId) == targetCommentId) {
          return comment;
        }

        // 在回复中查找
        if (comment.replies) {
          for (let reply of comment.replies) {
            if ((reply.commentId || reply.replyId) == targetCommentId) {
              return reply;
            }

            // 在子回复中查找
            if (reply.replies) {
              for (let subReply of reply.replies) {
                if (
                  (subReply.commentId || subReply.replyId) == targetCommentId
                ) {
                  return subReply;
                }
              }
            }
          }
        }
      }

      return null;
    },

    /**
     * 查找评论在列表中的索引
     */
    findCommentIndex(targetCommentId) {
      let currentIndex = 0;

      for (const comment of this.nestedCommentList) {
        // 检查顶级评论
        if (
          comment.commentId == targetCommentId ||
          comment.replyId == targetCommentId
        ) {
          return currentIndex;
        }
        currentIndex++;

        // 检查回复
        if (comment.replies) {
          for (const reply of comment.replies) {
            if (
              reply.commentId == targetCommentId ||
              reply.replyId == targetCommentId
            ) {
              return currentIndex;
            }
            currentIndex++;
          }
        }
      }

      return -1;
    },

    /**
     * 计算滚动位置
     */
    calculateScrollPosition(commentIndex) {
      // 每个评论大约200rpx高度，加上间距
      const commentHeight = 200;
      const scrollTop = commentIndex * commentHeight;

      console.log(
        "🎯 [智能跳转] 评论索引:",
        commentIndex,
        "计算高度:",
        scrollTop
      );

      return scrollTop;
    },

    /**
     * 滚动到指定位置
     */
    scrollToPosition(scrollTop) {
      // 使用scroll-view的scrollTop属性
      const scrollView = this.$refs.contentScroll;
      if (scrollView) {
        scrollView.scrollTop = scrollTop;
        console.log("🎯 [智能跳转] 使用scroll-view滚动到:", scrollTop);
      } else {
        // 备用方案：直接调用NestedComment组件的滚动方法
        if (this.$refs.nestedComment) {
          this.$refs.nestedComment.highlightComment(this.scrollToCommentId);
        }
        console.log(
          "🎯 [智能跳转] 使用NestedComment组件滚动到评论:",
          this.scrollToCommentId
        );
      }
    },

    /**
     * 高亮显示目标评论
     */
    highlightTargetComment() {
      console.log("🎯 [智能跳转] 高亮显示评论");
      // 这里可以添加高亮逻辑，比如给评论添加特殊样式
      // 由于评论在子组件中，可以通过refs调用子组件方法
      if (this.$refs.nestedComment) {
        this.$refs.nestedComment.highlightComment(this.scrollToCommentId);
      }
    },

    /**
     * 加载更多回复来找到目标评论
     */
    async loadMoreRepliesToFindTarget(targetCommentId) {
      console.log(
        "🎯 [智能跳转] 开始加载更多回复，寻找目标评论ID:",
        targetCommentId
      );

      // 防止重复加载
      if (this.loadingMoreComments) {
        console.log("🎯 [智能跳转] 正在加载中，跳过重复请求");
        return;
      }

      // 检查是否已经尝试过加载更多回复
      if (this.hasTriedLoadMoreReplies) {
        console.log("🎯 [智能跳转] 已经尝试过加载更多回复，跳过");
        return;
      }

      this.loadingMoreComments = true;
      this.hasTriedLoadMoreReplies = true;

      try {
        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        // 遍历所有顶级评论，尝试加载更多回复
        for (let comment of this.nestedCommentList) {
          const commentId = comment.commentId || comment.replyId;
          console.log(`🎯 [智能跳转] 尝试加载评论${commentId}的更多回复`);

          try {
            const response = await getMoreReplies(commentId, currentUserId);

            if (
              response.code === 200 &&
              response.data &&
              response.data.length > 0
            ) {
              // 更新回复列表
              comment.replies = response.data || [];
              console.log(
                `🎯 [智能跳转] 评论${commentId}加载了${response.data.length}条回复`
              );

              // 检查是否找到了目标评论
              const foundComment = this.findCommentInList(targetCommentId);
              if (foundComment) {
                console.log(
                  "🎯 [智能跳转] 找到目标评论，等待DOM更新后重新定位"
                );
                // 等待DOM更新后再重新尝试定位
                this.$nextTick(() => {
                  setTimeout(() => {
                    this.performScrollToComment();
                  }, 1000); // 给更多时间让DOM更新
                });
                return;
              }
            }
          } catch (error) {
            console.error(
              `🎯 [智能跳转] 加载评论${commentId}的回复失败:`,
              error
            );
          }
        }

        // 如果加载完所有回复后还没找到，尝试加载更多页面
        console.log("🎯 [智能跳转] 加载完所有回复后仍未找到，尝试加载更多页面");
        this.loadMoreCommentsToFindTarget(targetCommentId);
      } catch (error) {
        console.error("🎯 [智能跳转] 加载更多回复失败:", error);
        uni.showToast({
          title: "加载回复失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.loadingMoreComments = false;
      }
    },

    /**
     * 加载更多评论来找到目标评论
     */
    async loadMoreCommentsToFindTarget(targetCommentId) {
      console.log(
        "🎯 [智能跳转] 开始加载更多评论，寻找目标评论ID:",
        targetCommentId
      );

      // 防止重复加载
      if (this.loadingMoreComments) {
        console.log("🎯 [智能跳转] 正在加载中，跳过重复请求");
        return;
      }

      this.loadingMoreComments = true;

      try {
        // 尝试加载更多评论
        const userData = uni.getStorageSync("userData");
        const currentUserId = userData ? userData.userId : null;

        // 增加页码来加载更多评论
        const nextPage = this.commentPageNum + 1;

        console.log("🎯 [智能跳转] 加载第", nextPage, "页评论");

        const response = await getNestedComments(
          this.imagetextId,
          currentUserId,
          nextPage
        );

        if (
          response.code === 200 &&
          response.data &&
          response.data.length > 0
        ) {
          // 去重合并新评论到现有列表
          const existingIds = new Set(
            this.nestedCommentList.map(
              (comment) => comment.commentId || comment.replyId
            )
          );
          const newComments = response.data.filter((comment) => {
            const commentId = comment.commentId || comment.replyId;
            return !existingIds.has(commentId);
          });

          console.log(
            "🎯 [智能跳转] 新加载评论数量:",
            response.data.length,
            "去重后数量:",
            newComments.length
          );

          // 如果没有新评论，说明已经加载完了
          if (newComments.length === 0) {
            console.log("🎯 [智能跳转] 没有新评论了，目标评论可能已被删除");
            uni.showToast({
              title: "目标评论不存在或已被删除",
              icon: "none",
              duration: 2000,
            });
            return;
          }

          this.nestedCommentList = [...this.nestedCommentList, ...newComments];
          this.commentPageNum = nextPage;

          console.log(
            "🎯 [智能跳转] 加载了更多评论，总数:",
            this.nestedCommentList.length
          );

          // 检查是否找到了目标评论
          const foundComment = this.findCommentInList(targetCommentId);
          if (foundComment) {
            console.log("🎯 [智能跳转] 找到目标评论，重新尝试定位");
            // 重新尝试定位
            setTimeout(() => {
              this.performScrollToComment();
            }, 500);
          } else {
            console.log("🎯 [智能跳转] 仍未找到目标评论，继续加载更多");
            // 限制最大加载页数，避免无限循环
            if (nextPage <= 20) {
              // 最多加载20页
              setTimeout(() => {
                this.loadMoreCommentsToFindTarget(targetCommentId);
              }, 1000);
            } else {
              console.log(
                "🎯 [智能跳转] 已达到最大加载页数，目标评论可能已被删除"
              );
              uni.showToast({
                title: "目标评论不存在或已被删除",
                icon: "none",
                duration: 2000,
              });
            }
          }
        } else {
          console.log("🎯 [智能跳转] 没有更多评论了，目标评论可能已被删除");
          uni.showToast({
            title: "目标评论不存在或已被删除",
            icon: "none",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("🎯 [智能跳转] 加载更多评论失败:", error);
        uni.showToast({
          title: "加载评论失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.loadingMoreComments = false;
      }
    },

    /**
     * 在评论列表中查找指定ID的评论
     */
    findCommentInList(commentId) {
      for (const comment of this.nestedCommentList) {
        if (comment.commentId == commentId || comment.replyId == commentId) {
          return comment;
        }

        // 检查回复
        if (comment.replies) {
          for (const reply of comment.replies) {
            if (reply.commentId == commentId || reply.replyId == commentId) {
              return reply;
            }
          }
        }
      }
      return null;
    },

    // 预览作者头像
    previewAvatar() {
      const avatarUrl = this.imagetextDetail.avatar;
      if (!avatarUrl || avatarUrl === "/static/images/placeholder.png") {
        uni.showToast({
          title: "暂无头像图片",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      uni.previewImage({
        urls: [avatarUrl],
        current: avatarUrl,
        success: () => {
          console.log("头像预览成功");
        },
        fail: (error) => {
          console.error("头像预览失败:", error);
          uni.showToast({
            title: "图片预览失败",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },

    // 预览内容图片
    previewContentImages(currentIndex = 0) {
      if (
        !this.imagetextDetail.images ||
        this.imagetextDetail.images.length === 0
      ) {
        uni.showToast({
          title: "暂无内容图片",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      const imageUrls = this.imagetextDetail.images.map((img) => img.imageUrl);

      uni.previewImage({
        urls: imageUrls,
        current: currentIndex,
        success: () => {
          console.log("内容图片预览成功");
        },
        fail: (error) => {
          console.error("内容图片预览失败:", error);
          uni.showToast({
            title: "图片预览失败",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.imagetext-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.content-scroll {
  flex: 1;
  overflow: hidden;
}

.detail-content {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.author-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.author-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.author-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.author-text {
  flex: 1;
}

.author-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.follow-btn {
  background: #4caf50;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;

  &::after {
    border: none;
  }
}

.more-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:active {
    background: #e8e8e8;
  }
}

.content-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  padding: 30rpx;
  padding-bottom: 20rpx;
}

.content-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  padding: 0 30rpx 30rpx;
}

.content-images {
  padding: 0 30rpx 30rpx;
}

.image-item {
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.content-image {
  width: 100%;
  display: block;
}

.stats-section {
  display: flex;
  gap: 40rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 26rpx;
  color: #999;
}

.action-section {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  color: #666;

  text {
    &.liked {
      color: #ff4757;
    }
  }
}

.comment-section {
  margin-top: 20rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.comment-header {
  margin-bottom: 30rpx;
}

.comment-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.comment-item {
  display: flex;
  gap: 20rpx;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.comment-content {
  flex: 1;
}

.comment-author {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.author-badge {
  background: #4caf50;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  line-height: 1;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.no-comments {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
}

.no-comments-text {
  display: block;
  margin-top: 20rpx;
  font-size: 26rpx;
}

.comment-input-section {
  background: #fff;
  border-top: 1rpx solid #eee;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.comment-input-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: #f8f9fa;
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
}

.comment-input {
  flex: 1;
  font-size: 28rpx;
  background: transparent;
  border: none;
  outline: none;
}

.send-btn {
  background: #4caf50;
  color: #fff;
  border: none;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;

  &:disabled {
    background: #ccc;
  }

  &::after {
    border: none;
  }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  color: #999;
}

.error-text {
  margin: 30rpx 0;
  font-size: 28rpx;
}

.retry-btn {
  background: #4caf50;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;

  &::after {
    border: none;
  }
}

/* 评论高亮样式 */
.highlight-comment {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
  border: 2rpx solid #ffc107 !important;
  border-radius: 16rpx !important;
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}
</style>
