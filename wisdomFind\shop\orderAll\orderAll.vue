<template>
  <view class="order-container">
    <!-- 订单状态标签栏 -->
    <view class="status-tabs">
      <scroll-view scroll-x class="tabs-scroll" show-scrollbar="false">
        <view class="tabs-content">
          <view
            v-for="(tab, index) in orderTabs"
            :key="index"
            :class="['tab-item', { active: currentTab === index }]"
            @click="switchTab(index)"
          >
            <text class="tab-text">{{ tab.name }}</text>
            <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 订单列表 -->
    <scroll-view
      scroll-y
      class="order-list-scroll"
      @scrolltolower="loadMore"
      @refresherrefresh="onRefresh"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
    >
      <!-- 加载状态 -->
      <view v-if="isLoading && page === 1" class="loading-container">
        <uni-load-more status="loading" />
      </view>

      <!-- 订单列表内容 -->
      <view v-else-if="filteredOrders.length > 0" class="order-list">
        <view
          v-for="order in filteredOrders"
          :key="order.id"
          class="order-card"
          @click="viewOrderDetail(order)"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="shop-info">
              <view class="shop-icon">
                <uni-icons type="shop" size="16" color="#ff6b35" />
              </view>
              <text class="shop-name">智慧商城</text>
              <uni-icons type="right" size="12" color="#999" />
            </view>
            <view class="order-status">
              <text :class="['status-text', order.status]">{{
                getStatusText(order.status)
              }}</text>
            </view>
          </view>

          <!-- 商品信息 -->
          <view class="goods-section">
            <view class="goods-item">
              <image
                :src="
                  order.goodsImage ||
                  order.goodsImg ||
                  '/static/images/placeholder.png'
                "
                class="goods-img"
                mode="aspectFill"
                @error="handleImageError"
                @click.stop="previewGoodsImage(order)"
              />
              <view class="goods-info">
                <text class="goods-name">{{
                  order.goodsName || `商品ID: ${order.goodsId}`
                }}</text>
                <text class="goods-spec">{{
                  order.skuSpec || "默认规格"
                }}</text>
                <view class="goods-price-row">
                  <text class="goods-price"
                    >¥{{ formatPrice(order.price) }}</text
                  >
                  <text class="goods-quantity">x{{ order.amount }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 倒计时（仅待支付订单） -->
          <view
            v-if="
              order.status === 'pending' &&
              order.payDeadline &&
              order.stockLockStatus === 1
            "
            class="countdown-section"
          >
            <view class="countdown-info">
              <uni-icons type="clock" size="14" color="#ff6b35" />
              <text class="countdown-text"
                >支付剩余时间：{{ formatCountdown(order.payDeadline) }}</text
              >
            </view>
          </view>

          <!-- 订单统计 -->
          <view class="order-summary">
            <text class="total-count">共{{ order.amount || 0 }}件商品</text>
            <view class="total-amount">
              <text class="amount-label">实付款</text>
              <text class="amount-value"
                >¥{{ formatPrice(order.totalPrice || 0) }}</text
              >
            </view>
          </view>

          <!-- 订单操作 -->
          <view class="order-actions">
            <view class="action-buttons">
              <!-- 超时订单 -->
              <button
                v-if="isOrderTimeout(order)"
                class="btn-delete"
                @click.stop="deleteOrder(order)"
              >
                删除订单
              </button>

              <!-- 正常订单操作 -->
              <template v-else>
                <!-- 待支付 -->
                <template v-if="order.status === 'pending'">
                  <button
                    class="btn-secondary"
                    @click.stop="cancelOrder(order)"
                  >
                    取消订单
                  </button>
                  <button class="btn-primary" @click.stop="payOrder(order)">
                    立即支付
                  </button>
                </template>

                <!-- 待发货 -->
                <template v-if="order.status === 'paid'">
                  <button
                    class="btn-secondary"
                    @click.stop="remindShipment(order)"
                  >
                    提醒发货
                  </button>
                </template>

                <!-- 待收货 -->
                <template v-if="order.status === 'shipped'">
                  <button
                    class="btn-secondary"
                    @click.stop="viewLogistics(order)"
                  >
                    查看物流
                  </button>
                  <button
                    class="btn-primary"
                    @click.stop="confirmReceive(order)"
                  >
                    确认收货
                  </button>
                </template>

                <!-- 已完成 -->
                <template v-if="order.status === 'completed'">
                  <button
                    v-if="!order.hasReview"
                    class="btn-primary"
                    @click.stop="writeReview(order)"
                  >
                    评价晒单
                  </button>
                  <button class="btn-secondary" @click.stop="buyAgain(order)">
                    再次购买
                  </button>
                </template>

                <!-- 已取消 -->
                <template v-if="order.status === 'cancelled'">
                  <button class="btn-delete" @click.stop="deleteOrder(order)">
                    删除订单
                  </button>
                </template>
              </template>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more">
          <uni-load-more :status="loadMoreStatus" />
        </view>
      </view>
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon">
          <uni-icons type="shop" size="60" color="#ddd" />
        </view>
        <text class="empty-text">暂无订单</text>
        <text class="empty-desc">快去挑选心仪的商品吧</text>
        <button class="empty-btn" @click="goShopping">去逛逛</button>
      </view>
    </scroll-view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>
  </view>
</template>
 
<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
// import LazyImageV2 from "@/components/LazyImageV2.vue";
import {
  apiGetOrderList,
  apiGetOrderDetailList,
  apiCancelOrder,
  apiConfirmReceive,
  apiDeleteOrder,
  apiGetOrderStats,
} from "@/shop/api/order.js";
import { apiRemindShipment } from "@/shop/api/shipmentReminder.js";
import {
  connectWebSocket,
  registerMessageHandler,
  removeMessageHandler,
  isConnected,
} from "@/utils/websocketManager.js";
import {
  initAdminNotificationListener,
  isAdmin,
} from "@/utils/adminNotificationListener.js";

// 响应式数据
const showSearch = ref(false);
const searchKeyword = ref("");
const currentTab = ref(0);
const isLoading = ref(false);
const isRefreshing = ref(false);
const hasMore = ref(true);
const loadMoreStatus = ref("more");
const page = ref(1);
const pageSize = 1000; // 一次性加载更多，避免分页影响体验

// 商品信息缓存
const goodsCache = ref(new Map());

// 订单标签
const orderTabs = ref([
  { key: "all", name: "全部", count: 0 },
  { key: "pending", name: "待付款", count: 0 },
  { key: "paid", name: "待发货", count: 0 },
  { key: "shipped", name: "待收货", count: 0 },
  { key: "completed", name: "已完成", count: 0 },
  { key: "cancelled", name: "已取消", count: 0 },
]);

// 订单列表
const orders = ref([]);

// 计算属性
const filteredOrders = computed(() => {
  let filtered = orders.value;

  // 按状态筛选
  if (currentTab.value > 0) {
    const status = orderTabs.value[currentTab.value].key;
    const beforeFilter = filtered.length;
    filtered = filtered.filter((order) => order.status === status);
    console.log(`过滤${status}状态: ${beforeFilter} -> ${filtered.length}`);
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    const beforeSearch = filtered.length;
    filtered = filtered.filter(
      (order) =>
        order.orderNo.toLowerCase().includes(keyword) ||
        (order.goodsName && order.goodsName.toLowerCase().includes(keyword)) ||
        (order.goodsId && order.goodsId.toString().includes(keyword))
    );
    console.log(`关键词搜索: ${beforeSearch} -> ${filtered.length}`);
  }

  console.log(
    "最终过滤结果:",
    filtered.map((o) => ({ id: o.id, status: o.status, orderNo: o.orderNo }))
  );
  return filtered;
});

// 生命周期
onLoad((options) => {
  console.log("订单列表页面参数:", options);

  if (options.tab) {
    const tabIndex = orderTabs.value.findIndex(
      (tab) => tab.key === options.tab
    );
    if (tabIndex > -1) {
      currentTab.value = tabIndex;
    }
  }

  if (options.orderId) {
    // 如果有订单ID，跳转到订单详情
    viewOrderDetail({ id: options.orderId });
  }

  // 处理从购物车结算跳转过来的情况
  if (options.from === "checkout" && options.orderIds) {
    console.log("从购物车结算跳转，订单IDs:", options.orderIds);
    // 可以在这里高亮显示刚创建的订单
    const orderIdArray = options.orderIds.split(",");
    console.log("解析的订单ID数组:", orderIdArray);

    // 显示提示信息
    uni.showToast({
      title: `成功创建 ${orderIdArray.length} 个订单`,
      icon: "success",
      duration: 2000,
    });
  }
});

onShow(() => {
  loadOrders();
  refreshStats();
});

onMounted(() => {
  // 启动倒计时定时器
  startCountdownTimer();
  // 页面仅确保已连并注册处理器
  initWebSocket();
  // 初始化管理员通知监听（如果是管理员）
  initAdminNotificationListener();
});

onUnmounted(() => {
  // 清理定时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
  // 仅移除页面的消息处理器，不断开全局连接
  removeMessageHandler("orderStatusUpdate");
});

// 倒计时定时器
const countdownTimer = ref(null);
// 添加一个响应式变量来触发倒计时更新
const countdownUpdate = ref(0);

// 启动倒计时
const startCountdownTimer = () => {
  countdownTimer.value = setInterval(() => {
    // 触发倒计时更新
    countdownUpdate.value++;

    // 更新待支付订单的倒计时
    orders.value.forEach((order) => {
      if (
        order.status === "pending" &&
        order.payDeadline &&
        order.stockLockStatus === 1
      ) {
        const now = new Date().getTime();
        const formattedDeadline = order.payDeadline.replace(" ", "T");
        const deadline = new Date(formattedDeadline).getTime();
        if (now >= deadline) {
          // 支付超时，更新订单状态
          order.status = "cancelled";
          order.statusText = "已取消";
          console.log("订单超时自动取消:", order.orderNo);
        }
      }
    });
  }, 1000);
};

// 连接WebSocket
const initWebSocket = () => {
  try {
    const userData = uni.getStorageSync("userData");
    if (userData && userData.userId && userData.token) {
      if (!isConnected()) {
        connectWebSocket(userData.userId.toString(), userData.token).catch(
          () => {}
        );
      }
      registerMessageHandler("orderStatusUpdate", (orderEvent) => {
        handleOrderStatusUpdate(orderEvent);
      });
    }
  } catch (error) {
    console.error("初始化WebSocket失败:", error);
  }
};

// 处理订单状态更新
const handleOrderStatusUpdate = (orderEvent) => {
  try {
    // 查找对应的订单并更新状态
    const orderIndex = orders.value.findIndex(
      (order) => order.id === orderEvent.orderId
    );
    if (orderIndex !== -1) {
      const order = orders.value[orderIndex];
      const oldStatus = order.status;
      const newStatus = mapBackendStatusToFrontend(orderEvent.status);

      if (oldStatus !== newStatus) {
        console.log(
          `实时更新订单状态: ${order.orderNo} ${oldStatus} -> ${newStatus}`
        );
        order.status = newStatus;
        order.statusText = getStatusText(newStatus);

        // 显示状态更新提示
        uni.showToast({
          title: `订单${order.orderNo}状态已更新为${getStatusText(newStatus)}`,
          icon: "success",
          duration: 2000,
        });
      }
    }
  } catch (error) {
    console.error("处理订单状态更新失败:", error);
  }
};

// 切换标签
const switchTab = (index) => {
  currentTab.value = index;
  page.value = 1;
  hasMore.value = true;
  loadOrders();
  refreshStats();
};

// 去购物
const goShopping = () => {
  uni.switchTab({
    url: "/pages/store/store",
  });
};

// 加载订单列表
const loadOrders = async () => {
  try {
    if (page.value === 1) {
      isLoading.value = true;
    }

    const currentStatus =
      currentTab.value > 0 ? orderTabs.value[currentTab.value].key : "";
    const backendStatus = currentStatus
      ? mapFrontendStatusToBackend(currentStatus)
      : "";

    // 优先使用新的关联查询API
    let response;
    const apiParams = {
      page: page.value,
      pageSize: pageSize,
      status: backendStatus,
      keyword: searchKeyword.value,
    };
    console.log("调用新API参数:", apiParams);

    try {
      response = await apiGetOrderDetailList(apiParams);
      console.log("新API调用成功:", response);
    } catch (error) {
      console.log("新API失败，回退到旧API:", error);
      // 如果新API失败，回退到旧API
      response = await apiGetOrderList(apiParams);
      console.log("旧API调用结果:", response);
    }

    if (response && response.rows) {
      console.log("后端返回的订单数据:", response.rows);

      let newOrders;
      // 检查是否已包含商品信息
      if (response.rows[0] && response.rows[0].goodsName) {
        // 新API返回的数据，已包含商品信息
        console.log("使用新API数据，已包含商品信息");
        newOrders = response.rows.map((order) => {
          return {
            ...order,
            status: mapBackendStatusToFrontend(order.status),
            totalQuantity: order.amount || 0,
            totalAmount: order.totalPrice || 0,
            hasReview: order.hasReview || false,
          };
        });
      } else {
        // 旧API返回的数据，需要获取商品信息
        console.log("使用旧API数据，需要获取商品信息");
        newOrders = await Promise.all(
          response.rows.map(async (order) => {
            // 获取商品详情（使用缓存）
            const goodsInfo = await getGoodsInfo(order.goodsId);

            return {
              ...order,
              ...goodsInfo,
              status: mapBackendStatusToFrontend(order.status),
              totalQuantity: order.amount || 0,
              totalAmount: order.totalPrice || 0,
              hasReview: order.hasReview || false,
            };
          })
        );
      }

      // 调试信息：显示加载的订单状态分布
      const statusCount = newOrders.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {});
      console.log("当前加载的订单状态分布:", statusCount);
      console.log(
        "当前标签页:",
        currentTab.value,
        orderTabs.value[currentTab.value]?.name
      );

      if (page.value === 1) {
        orders.value = newOrders;
      } else {
        orders.value.push(...newOrders);
      }

      hasMore.value = newOrders.length === pageSize;
      loadMoreStatus.value = hasMore.value ? "more" : "noMore";

      // 计数交由后端统计结果覆盖
    }
  } catch (error) {
    uni.showToast({
      title: "加载失败",
      icon: "none",
    });
  } finally {
    isLoading.value = false;
    isRefreshing.value = false;
  }
};

// 删除前端分页统计逻辑，统一使用后端统计

// 全量统计，覆盖标签计数
const refreshStats = async () => {
  try {
    const userData = uni.getStorageSync("userData");
    const userId = userData && userData.userId;
    if (!userId) return;
    const resp = await apiGetOrderStats(userId);
    if (resp && resp.code === 200 && resp.data) {
      const s = resp.data;
      orderTabs.value.forEach((tab) => {
        if (s.hasOwnProperty(tab.key)) {
          tab.count = s[tab.key] || 0;
        }
      });
    }
  } catch (e) {
    console.warn("刷新订单统计失败", e);
  }
};

// 下拉刷新
const onRefresh = async () => {
  isRefreshing.value = true;
  page.value = 1;
  hasMore.value = false;
  await loadOrders();
  await refreshStats();
};

// 加载更多
const loadMore = async () => {
  if (!hasMore.value || isLoading.value) return;

  page.value++;
  await loadOrders();
  await refreshStats();
};

// 查看订单详情
const viewOrderDetail = (order) => {
  uni.navigateTo({
    url: `/shop/orderDetail/orderDetail?id=${order.id}`,
  });
};

// 查看商品详情
const viewGoodsDetail = (goodsId) => {
  uni.navigateTo({
    url: `/shop/goods/goods?id=${goodsId}`,
  });
};

// 图片URL解码处理
const decodeImageUrl = (url) => {
  if (!url) {
    return "/static/images/placeholder.png";
  }
  try {
    // 解码URL中的中文字符
    const decodedUrl = decodeURIComponent(url);
    console.log("原始URL:", url);
    console.log("解码后URL:", decodedUrl);
    return decodedUrl;
  } catch (error) {
    console.error("URL解码失败:", error);
    return url;
  }
};

// 图片加载处理
const handleImageError = (e) => {
  console.error("图片加载失败:", e);
  console.error("失败的图片URL:", e.target?.src || e.detail?.src);
  console.error("错误详情:", e.detail);

  // 尝试显示错误信息
  uni.showToast({
    title: "图片加载失败",
    icon: "none",
    duration: 2000,
  });
};

const handleImageLoad = (e) => {
  console.log("图片加载成功:", e.target?.src || e.detail?.src);
};

// 预览商品图片
const previewGoodsImage = (order) => {
  const imageUrl = order.goodsImage || order.goodsImg;
  if (!imageUrl || imageUrl === "/static/images/placeholder.png") {
    uni.showToast({
      title: "暂无商品图片",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
    success: () => {
      console.log("商品图片预览成功");
    },
    fail: (error) => {
      console.error("商品图片预览失败:", error);
      uni.showToast({
        title: "图片预览失败",
        icon: "none",
        duration: 2000,
      });
    },
  });
};

// 检查订单是否超时
const isOrderTimeout = (order) => {
  if (!order || !order.payDeadline || order.stockLockStatus !== 1) return false;
  const now = new Date().getTime();
  const formattedDeadline = order.payDeadline.replace(" ", "T");
  const deadline = new Date(formattedDeadline).getTime();
  return now >= deadline;
};

// 支付订单
const payOrder = (order) => {
  uni.navigateTo({
    url: `/shop/payment/payment?orderId=${order.id}&amount=${order.totalAmount}`,
  });
};

// 取消订单
const cancelOrder = (order) => {
  uni.showModal({
    title: "确认取消",
    content: "确定要取消这个订单吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          await apiCancelOrder(order.id);
          uni.showToast({ title: "订单已取消", icon: "success" });
          loadOrders(); // 重新加载订单列表
          refreshStats();
        } catch (error) {
          uni.showToast({ title: "取消失败", icon: "none" });
        }
      }
    },
  });
};

// 确认收货
const confirmReceive = (order) => {
  uni.showModal({
    title: "确认收货",
    content: "确认已收到商品吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await apiConfirmReceive(order.id);

          if (response && response.code === 200) {
            uni.showToast({ title: "确认成功", icon: "success" });

            // 立即更新本地订单状态
            const orderIndex = orders.value.findIndex((o) => o.id === order.id);
            if (orderIndex !== -1) {
              orders.value[orderIndex].status = "completed";
            }

            // 重新加载数据确保同步
            await loadOrders();
            await refreshStats();

            // 如果当前在"待收货"标签页，提示用户切换到"已完成"标签页查看
            if (currentTab.value === 3) {
              // 待收货标签页
              setTimeout(() => {
                uni.showModal({
                  title: "提示",
                  content: '订单已确认收货，是否切换到"已完成"标签页查看？',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      switchTab(4); // 切换到已完成标签页
                    }
                  },
                });
              }, 1000);
            }
          } else {
            uni.showToast({ title: response?.msg || "确认失败", icon: "none" });
          }
        } catch (error) {
          console.error("确认收货异常:", error);
          uni.showToast({ title: "确认失败", icon: "none" });
        }
      }
    },
  });
};

// 查看物流
const viewLogistics = (order) => {
  uni.navigateTo({
    url: `/shop/logistics/logistics-simple?orderId=${order.id}`,
  });
};

// 提醒发货
const remindShipment = async (order) => {
  try {
    const response = await apiRemindShipment(order.id);
    console.log("提醒发货响应:", response);

    if (response.code === 200) {
      uni.showToast({ title: "已提醒商家发货", icon: "success" });
      // 重新加载订单列表
      loadOrders();
      refreshStats();
    } else if (
      response.code === 500 &&
      response.msg &&
      response.msg.includes("uk_order_remind_date")
    ) {
      // 今天已经提醒过了
      uni.showToast({ title: "今天已经提醒过了", icon: "none" });
    } else {
      uni.showToast({ title: response.msg || "提醒失败", icon: "none" });
    }
  } catch (error) {
    // 友好提示，不在控制台报错
    const errMsg = error && error.msg ? error.msg : "提醒失败，请稍后重试";
    if (
      errMsg.includes("今天已经提醒过了") ||
      errMsg.includes("uk_order_remind_date")
    ) {
      uni.showToast({ title: "今天已经提醒过了，请明天再试", icon: "none" });
    } else {
      uni.showToast({ title: errMsg, icon: "none" });
    }
  }
};

// 评价订单
const writeReview = (order) => {
  uni.navigateTo({
    url: `/shop/review/review?orderId=${order.id}`,
  });
};

// 再次购买
const buyAgain = (order) => {
  // 将订单商品重新加入购物车
  order.items.forEach((item) => {
    // 这里需要调用加入购物车的API
    console.log("再次购买商品:", item);
  });
  uni.showToast({ title: "已加入购物车", icon: "success" });
};

// 删除订单
const deleteOrder = (order) => {
  uni.showModal({
    title: "确认删除",
    content: "删除后无法恢复，确定要删除吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          await apiDeleteOrder(order.id);
          uni.showToast({ title: "删除成功", icon: "success" });
          loadOrders();
        } catch (error) {
          uni.showToast({ title: "删除失败", icon: "none" });
        }
      }
    },
  });
};

// 工具函数
const formatTime = (time) => {
  if (!time) return "";

  // 修复时间格式，将空格替换为T以兼容iOS
  const formattedTime = time.toString().replace(" ", "T");
  const date = new Date(formattedTime);
  const now = new Date();
  const diff = now - date;

  if (diff < 60000) return "刚刚";
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`;

  return date.toLocaleDateString();
};

const formatPrice = (price) => {
  if (price === null || price === undefined || price === "" || isNaN(price)) {
    return "0.00";
  }
  return Number(price).toFixed(2);
};

// 后端状态映射到前端状态
const mapBackendStatusToFrontend = (backendStatus) => {
  const statusMap = {
    0: "pending", // 待支付
    1: "paid", // 已支付
    2: "shipped", // 已发货
    3: "completed", // 已完成
    4: "cancelled", // 已取消
  };
  return statusMap[backendStatus] || "pending";
};

// 前端状态映射到后端状态
const mapFrontendStatusToBackend = (frontendStatus) => {
  const statusMap = {
    pending: 0, // 待支付
    paid: 1, // 已支付
    shipped: 2, // 已发货
    completed: 3, // 已完成
    cancelled: 4, // 已取消
  };
  return statusMap[frontendStatus];
};

const formatCountdown = (deadline) => {
  // 添加对 countdownUpdate 的依赖，确保倒计时能够响应式更新
  countdownUpdate.value;

  if (!deadline) return "";

  // 修复时间格式，将空格替换为T以兼容iOS
  const formattedDeadline = deadline.toString().replace(" ", "T");

  const now = new Date().getTime();
  const deadlineTime = new Date(formattedDeadline).getTime();
  const diff = deadlineTime - now;

  if (diff <= 0) return "交易关闭";

  const hours = Math.floor(diff / 3600000);
  const minutes = Math.floor((diff % 3600000) / 60000);
  const seconds = Math.floor((diff % 60000) / 1000);

  // 确保返回有效的倒计时文本
  if (hours > 0) {
    return `剩余${hours}小时${minutes}分`;
  } else if (minutes > 0) {
    return `剩余${minutes}分${seconds}秒`;
  } else if (seconds > 0) {
    return `剩余${seconds}秒`;
  } else {
    return "交易关闭";
  }
};

// 获取商品信息（带缓存）
const getGoodsInfo = async (goodsId) => {
  if (!goodsId)
    return {
      goodsName: "未知商品",
      goodsImg: "/static/default-goods.png",
      goodsDesc: "",
    };

  // 先检查缓存
  if (goodsCache.value.has(goodsId)) {
    return goodsCache.value.get(goodsId);
  }

  try {
    const goodsResponse = await apiGetGoodsDetail(goodsId);
    if (goodsResponse && goodsResponse.data) {
      const goodsInfo = {
        goodsName: goodsResponse.data.goodsName,
        goodsImg: goodsResponse.data.goodsImage || goodsResponse.data.goodsImg,
        goodsDesc: goodsResponse.data.goodsDesc,
      };
      // 缓存商品信息
      goodsCache.value.set(goodsId, goodsInfo);
      return goodsInfo;
    }
  } catch (error) {
    console.log("获取商品详情失败:", error);
  }

  // 失败时返回默认信息
  const defaultInfo = {
    goodsName: `商品ID: ${goodsId}`,
    goodsImg: "/static/default-goods.png",
    goodsDesc: "",
  };
  // 也缓存失败的信息，避免重复请求
  goodsCache.value.set(goodsId, defaultInfo);
  return defaultInfo;
};

const getStatusText = (status) => {
  const statusMap = {
    pending: "待付款",
    paid: "待发货",
    shipped: "待收货",
    completed: "已完成",
    cancelled: "已取消",
    // 支持数字状态值
    0: "待付款",
    1: "待发货",
    2: "待收货",
    3: "已完成",
    4: "已取消",
  };
  return statusMap[status] || "未知状态";
};

const getEmptyDesc = () => {
  if (currentTab.value === 0) return "还没有订单，快去挑选心仪的商品吧";
  const tab = orderTabs.value[currentTab.value];
  return `暂无${tab.name}的订单`;
};
</script>

<style scoped lang="scss">
.order-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.status-tabs {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;

  .tabs-scroll {
    white-space: nowrap;
    padding: 0 16px;
    width: 100%;
    box-sizing: border-box;
  }

  .tabs-content {
    display: flex;
    gap: 6px;
    padding: 6px 0;
    width: 100%;
    box-sizing: border-box;
  }

  .tab-item {
    position: relative;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    gap: 3px;
    border-radius: 12px;
    background: #f8f9fa;
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;

    .tab-text {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .tab-badge {
      background: #ef4444;
      color: #fff;
      font-size: 8px;
      padding: 1px 3px;
      border-radius: 6px;
      min-width: 12px;
      text-align: center;
      line-height: 1.2;
      font-weight: 600;
    }

    &.active {
      background: #0ea5e9;
      border-color: #0ea5e9;
      transform: scale(1.01);

      .tab-text {
        color: #fff;
        font-weight: 600;
      }

      .tab-badge {
        background: #fff;
        color: #0ea5e9;
      }
    }
  }
}

.order-list-scroll {
  flex: 1;
  padding: 16px;
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  width: 100%;
}

.order-list {
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.order-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);
  }

  .order-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .shop-info {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .shop-icon {
        padding: 6px;
        background: #fff5f2;
        border-radius: 8px;
      }

      .shop-name {
        font-size: 15px;
        color: #333;
        font-weight: 600;
      }
    }

    .order-status {
      text-align: right;

      .status-text {
        font-size: 14px;
        font-weight: 600;

        &.pending {
          color: #ef4444;
        }
        &.paid {
          color: #0ea5e9;
        }
        &.shipped {
          color: #10b981;
        }
        &.completed {
          color: #8b5cf6;
        }
        &.cancelled {
          color: #64748b;
        }
      }
    }
  }

  .goods-section {
    padding: 16px 20px;

    .goods-item {
      display: flex;
      gap: 12px;

      .goods-img {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        background: #f5f5f5;
        flex-shrink: 0;
      }

      .goods-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .goods-name {
          font-size: 15px;
          color: #333;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          margin-bottom: 4px;
        }

        .goods-spec {
          font-size: 12px;
          color: #999;
          margin-bottom: 8px;
        }

        .goods-price-row {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .goods-price {
            font-size: 16px;
            color: #333;
            font-weight: 600;
          }

          .goods-quantity {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }

  .countdown-section {
    padding: 12px 20px;
    border-top: 1px solid #f5f5f5;
    background: #fff5f2;

    .countdown-info {
      display: flex;
      align-items: center;
      gap: 6px;
      background: #fff;
      border: 1px solid #ef4444;
      border-radius: 8px;
      padding: 8px 12px;

      .countdown-text {
        font-size: 13px;
        color: #ef4444;
        font-weight: 600;
      }
    }
  }

  .order-summary {
    padding: 16px 20px;
    border-top: 1px solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .total-count {
      font-size: 13px;
      color: #666;
    }

    .total-amount {
      display: flex;
      align-items: center;
      gap: 8px;

      .amount-label {
        font-size: 13px;
        color: #666;
      }

      .amount-value {
        font-size: 16px;
        color: #333;
        font-weight: 600;
      }
    }
  }

  .order-actions {
    padding: 16px 20px;
    border-top: 1px solid #f5f5f5;

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;

      .btn-primary {
        background: #0ea5e9;
        color: #fff;
        border: none;
        border-radius: 20px;
        padding: 8px 20px;
        font-size: 14px;
        font-weight: 500;
        min-width: 80px;
        transition: all 0.2s ease;

        &:active {
          background: #0284c7;
          transform: scale(0.95);
        }
      }

      .btn-secondary {
        background: #fff;
        color: #666;
        border: 1px solid #ddd;
        border-radius: 20px;
        padding: 8px 20px;
        font-size: 14px;
        font-weight: 500;
        min-width: 80px;
        transition: all 0.2s ease;

        &:active {
          background: #f5f5f5;
          transform: scale(0.95);
        }
      }

      .btn-delete {
        background: #ef4444;
        color: #fff;
        border: none;
        border-radius: 20px;
        padding: 8px 20px;
        font-size: 14px;
        font-weight: 500;
        min-width: 80px;
        transition: all 0.2s ease;

        &:active {
          background: #dc2626;
          transform: scale(0.95);
        }
      }
    }
  }
}

.load-more {
  padding: 20px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;

  .empty-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
    background: #f8f9fa;
    border-radius: 60px;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 16px;
    color: #999;
    margin-bottom: 24px;
  }

  .empty-desc {
    font-size: 14px;
    color: #999;
    margin-bottom: 24px;
  }

  .empty-btn {
    background: #ff6034;
    color: #fff;
    border: none;
    border-radius: 24px;
    padding: 12px 32px;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:active {
      background: #e55a2e;
      transform: scale(0.95);
    }
  }
}

.safe-area {
  height: env(safe-area-inset-bottom);
  background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .order-list-scroll {
    padding: 12px;
  }

  .order-card {
    .order-header,
    .goods-section,
    .countdown-section,
    .order-summary,
    .order-actions {
      padding-left: 16px;
      padding-right: 16px;
    }
  }
}

/* 确保在大屏幕上也有合适的最大宽度 */
@media (min-width: 768px) {
  .order-list {
    max-width: 600px;
  }

  .order-list-scroll {
    display: flex;
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.order-card {
  animation: fadeIn 0.3s ease-out;
}

/* 加载状态样式 */
.loading-container {
  .uni-load-more {
    .uni-load-more__img {
      width: 24px;
      height: 24px;
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
</style>
